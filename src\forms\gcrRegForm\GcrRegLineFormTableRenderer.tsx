import { Add, Close } from '@mui/icons-material';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { LineErrors } from '@src/components/LineErrors';
import { DEBUG } from '@src/constants';
import { GcrRegLineTextField } from './GcrRegLineTextField';
import { mockLines } from './gcrRegMockData';
import { useGcrRegStore } from './gcrRegStore';

type ScrLineFormProps = {
  handleAddLine: () => void;
};

export const GcrRegLineFormTableRenderer: React.FC<ScrLineFormProps> = (props) => {
  const { handleAddLine } = props;

  const { formLineIndex, clearFormLine, setFormLinePartial } = useGcrRegStore();
  // console.log(`errors`, errors);

  const isEdit = formLineIndex !== null;

  return (
    <>
      <Table size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '2%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* 5 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* 10 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* <col style={{ width: '3%' }} /> */}
          {/* <col style={{ width: '4%' }} /> */}
          {/* 15 */}
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell></TableCell>
            <TableCell align="center">C/R/N/D</TableCell>
            <TableCell align="center">Acreg</TableCell>
            <TableCell>Date</TableCell>
            <TableCell align="center">Seats</TableCell>
            {/* 5 */}
            <TableCell align="center">A/C</TableCell>
            <TableCell align="center">Origin</TableCell>
            <TableCell align="center">Prev</TableCell>
            <TableCell align="center">Time</TableCell>
            <TableCell align="center">O/N</TableCell>
            {/* 10 */}
            <TableCell align="center">Next</TableCell>
            <TableCell align="center">Dest</TableCell>
            <TableCell align="center">St</TableCell>
            {/* 13 */}
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell component="th" scope="row">
              Arr
            </TableCell>
            {/* Action */}
            <TableCell rowSpan={2}>
              <GcrRegLineTextField currentInputKey="action" />
            </TableCell>
            {/* Aircraft Registration */}
            <TableCell rowSpan={2}>
              <GcrRegLineTextField currentInputKey="aircraftRegistration" />
            </TableCell>
            {/* Date  */}
            <TableCell rowSpan={2}>
              <GcrRegLineTextField currentInputKey="date" />
            </TableCell>
            {/* Seats */}
            <TableCell rowSpan={2}>
              <GcrRegLineTextField currentInputKey="seats" />
            </TableCell>
            {/* Aircraft Type */}
            <TableCell rowSpan={2}>
              <GcrRegLineTextField currentInputKey="aircraftType" />
            </TableCell>
            {/* Origin */}
            <TableCell>
              <GcrRegLineTextField currentInputKey="origin" />
            </TableCell>
            {/* Previous */}
            <TableCell>
              <GcrRegLineTextField currentInputKey="previous" />
            </TableCell>
            {/* Time 1 */}
            <TableCell>
              <GcrRegLineTextField currentInputKey="timeArr" />
            </TableCell>
            {/* O/N */}
            <TableCell>{/* O/N */}</TableCell>
            {/* Next */}
            <TableCell>{/* Next */}</TableCell>
            {/* Destination */}
            <TableCell>{/* Dest */}</TableCell>
            {/* Service Type*/}
            <TableCell>
              <GcrRegLineTextField currentInputKey="stArr" />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              Dep
            </TableCell>
            {/* Action */}
            {/* rowSpan={2} */}
            {/* Date Range */}
            {/* rowSpan={2} */}
            {/* DOOP */}
            {/* rowSpan={2} */}
            {/* Seats */}
            {/* rowSpan={2} */}
            {/* Aircraft Type */}
            {/* rowSpan={2} */}
            {/* Origin */}
            <TableCell>{/* Origin */}</TableCell>
            {/* Previous */}
            <TableCell>{/* Prev */}</TableCell>
            {/* Time 2 */}
            <TableCell>
              <GcrRegLineTextField currentInputKey="timeDep" />
            </TableCell>
            {/* O/N */}
            <TableCell>
              {/* O/N */}
              <GcrRegLineTextField currentInputKey="on" />
            </TableCell>
            {/* Next */}
            <TableCell>
              {/* Next */}
              <GcrRegLineTextField currentInputKey="next" />
            </TableCell>
            {/* Destination */}
            <TableCell>
              {/* Dest */}
              <GcrRegLineTextField currentInputKey="destination" />
            </TableCell>
            {/* Service Type*/}
            <TableCell>
              <GcrRegLineTextField currentInputKey="stDep" />
            </TableCell>
            {/* Frequency */}
            {/* rowSpan={2} */}
            {/* Aircraft Registration */}
            {/* rowSpan={2} */}
          </TableRow>
        </TableBody>
      </Table>

      <LineErrors useStore={useGcrRegStore} />

      {DEBUG && (
        <TableFormInnerBox className="right">
          {mockLines.map((line, i) => (
            <MockButton key={i} onClick={() => setFormLinePartial({ ...line })}>
              {i}
            </MockButton>
          ))}
        </TableFormInnerBox>
      )}

      <TableFormInnerBox className="right">
        <FormButton
          variant="contained"
          color="secondary"
          size="small"
          onClick={() => clearFormLine()}
          startIcon={<Close />}
        >
          Clear
        </FormButton>
        <FormButton variant="contained" size="small" onClick={handleAddLine} startIcon={<Add />}>
          {isEdit ? `Update Line` : `Add Line`}
        </FormButton>
      </TableFormInnerBox>
    </>
  );
};
