import type { FormInputEvent } from '@src/typesGlobal';
import type { GcrFltMessage } from './gcrFltTypes';
import { gcrFltMessageFormConfig } from './gcrFltMessageFormConfig';

/**
 * 'getOnChange' will given a field key, and a store method to update the message,
 * return a function that takes an event
 * and updates the store for that field.
 * Memoize it to prevent unnecessary re-renders.
 */
export const getOnChange =
  (key: keyof GcrFltMessage, setFormMsg: (updates: Partial<GcrFltMessage>) => void) => (e: FormInputEvent) => {
    let value: GcrFltMessage[keyof GcrFltMessage] = e.target.value;
    // Check if this field should be uppercased
    if (gcrFltMessageFormConfig.inputs[key]?.toUpperCase) {
      value = e.target.value?.toUpperCase();
    }
    // Check if this field should be a number type
    // if (gcrFltMessageFormConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
    //   value = Number(e.target.value);
    // }
    setFormMsg({ [key]: value });
  };
