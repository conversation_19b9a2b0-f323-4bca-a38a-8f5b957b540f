import { LineList } from '@src/components/LineList';
import { validateActionSequence } from '@src/validation/actionSequenceValidation';
import { useEffect, useMemo, useState } from 'react';
import { formatScrLine } from './scrUtils';
import { StructuralPrefix } from './scrValidation';
import { useScrStore } from './useScrStore';

export const ScrLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, formLineErrors, moveLineByOne, clearAllLines } = useScrStore();
  const [lineSeqErrors, setLineSeqErrors] = useState<Record<string, string>>({});

  // Check for C/R action sequence errors
  useEffect(() => {
    setLineSeqErrors(validateActionSequence(lines) || {});
  }, [lines]);

  const lineErrors = formLineErrors || {};
  // Sort out the errors that are structural (ie not for specific input fields):
  const errors = Object.keys(lineErrors).reduce<Record<string, string>>(
    (acc, curr) => {
      if (curr.startsWith(StructuralPrefix)) {
        acc[curr.replace(StructuralPrefix, '')] = lineErrors[curr];
      }
      return acc;
    },
    { ...lineSeqErrors },
  );

  // Format the lines for display
  const formattedLines = useMemo(() => {
    return lines.map(formatScrLine);
  }, [lines]);

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={setFormLine}
      handleDelete={deleteLine}
      handleMoveUp={(index) => moveLineByOne(index, 'up')}
      handleMoveDown={(index) => moveLineByOne(index, 'down')}
      clearAllLines={clearAllLines}
      errors={errors}
      hasErrors={hasErrors}
    />
  );
};
