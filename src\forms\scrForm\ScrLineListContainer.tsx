import { LineList } from '@src/components/LineList';
import { useMemo } from 'react';
import { formatScrLine } from './scrUtils';
import { useScrStore } from './useScrStore';
import { StructuralPrefix } from './scrValidation';

export const ScrLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, formLineErrors, moveLineByOne } = useScrStore();

  const lineErrors = formLineErrors || {};
  const errors = Object.keys(lineErrors).reduce<Record<string, string>>((acc, curr) => {
    if (curr.startsWith(StructuralPrefix)) {
      acc[curr.replace(StructuralPrefix, '')] = lineErrors[curr];
    }
    return acc;
  }, {});

  const formattedLines = useMemo(() => {
    return lines.map(formatScrLine);
  }, [lines]);

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={setFormLine}
      handleDelete={deleteLine}
      handleMoveUp={(index) => moveLineByOne(index, 'up')}
      handleMoveDown={(index) => moveLineByOne(index, 'down')}
      errors={errors}
      hasErrors={hasErrors}
    />
  );
};
