import { LineList } from '@src/components/LineList';
import { useMemo } from 'react';
import { formatScrLine } from './scrUtils';
import { useScrStore } from './useScrStore';

export const ScrLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, moveLineByOne, clearAllLines } = useScrStore();

  // Format the lines for display
  const formattedLines = useMemo(() => {
    return lines.map(formatScrLine);
  }, [lines]);

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={setFormLine}
      handleDelete={deleteLine}
      handleMoveUp={(index) => moveLineByOne(index, 'up')}
      handleMoveDown={(index) => moveLineByOne(index, 'down')}
      clearAllLines={clearAllLines}
      errors={{}}
      hasErrors={false}
    />
  );
};
