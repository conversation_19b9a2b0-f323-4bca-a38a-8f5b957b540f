import Delete from '@mui/icons-material/Delete';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';

type SMessagePreviewProps = {
  clearMessage: () => void;
  msgHeader: string | null;
  msgFooter: string | null;
};

export const MessagePreview: React.FC<SMessagePreviewProps> = (props) => {
  const { clearMessage, msgHeader, msgFooter } = props;

  return (
    <TableContainer component={Paper} sx={{ mb: 2, mx: 'auto' }}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Message</TableCell>
            <TableCell sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              Output
              <IconButton size="small" onClick={() => clearMessage()}>
                <Delete fontSize="small" />
              </IconButton>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {msgHeader && msgFooter ? (
            <>
              <TableRow>
                <TableCell>Header</TableCell>
                <TableCell>
                  <pre style={{ margin: 0, fontFamily: 'monospace' }}>{msgHeader}</pre>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Footer</TableCell>
                <TableCell>
                  <pre style={{ margin: 0, fontFamily: 'monospace' }}>{msgFooter}</pre>
                </TableCell>
              </TableRow>
            </>
          ) : (
            <TableRow>
              <TableCell colSpan={2} align="center">
                No message added
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
