import { LineList } from '@src/components/LineList';
import { useMemo } from 'react';
import { formatGcrFltLine } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';
import { StructuralPrefix } from './gcrFltValidation';

export const GcrFltLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, formLineErrors } = useGcrFltStore();

  const lineErrors = formLineErrors || {};
  const errors = Object.keys(lineErrors).reduce<Record<string, string>>((acc, curr) => {
    if (curr.startsWith(StructuralPrefix)) {
      acc[curr.replace(StructuralPrefix, '')] = lineErrors[curr];
    }
    return acc;
  }, {});

  const formattedLines = useMemo(() => {
    return lines.map(formatGcrFltLine);
  }, [lines]);

  // console.log(`errors`, errors);

  const hasErrors = Object.keys(errors).length > 0;

  const handleEdit = (index: number) => {
    setFormLine(index);
  };

  const handleDelete = (index: number) => {
    deleteLine(index);
  };

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={handleEdit}
      handleDelete={handleDelete}
      errors={errors}
      hasErrors={hasErrors}
    />
  );
};
