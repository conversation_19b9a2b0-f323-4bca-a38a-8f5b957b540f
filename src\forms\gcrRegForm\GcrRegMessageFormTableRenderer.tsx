import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import { ErrorLabel, FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { DEBUG } from '@src/constants';
import type { Maybe } from '@src/typesGlobal';
import { zodErrorMap } from '@src/utils/utils';
import { useRef } from 'react';
import { gcrRegMessageFormConfig as formConfig } from './gcrRegMessageFormConfig';
import { getOnChange } from './gcrRegMessageFormUtils';
import { mockMessages } from './gcrRegMockData';
import { useGcrRegStore } from './gcrRegStore';
import { gcrRegMessageSchema } from './gcrRegValidation';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

export const GcrRegMessageFormTableRenderer: React.FC = () => {
  const { inputs } = formConfig;
  const {
    setMessage,
    formMessage,
    setFormMessagePartial: setMsgPart,
    clearFormMessage,
    formMessageErrors: errors,
    setFormMessageErrors,
    clearFormMessageErrors,
  } = useGcrRegStore();

  const formRef = useRef<HTMLDivElement>(null);

  const handleAddMessage = () => {
    const parsed = gcrRegMessageSchema.safeParse(formMessage);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log('errorMap', errorMap);
      setFormMessageErrors(errorMap);
      return;
    }
    setMessage(parsed.data);
    clearFormMessageErrors();
  };

  return (
    <div ref={formRef}>
      <Table /* component={Paper} */ size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '20%' }} />
          <col style={{ width: '20%' }} />
          <col style={{ width: '15%' }} />
          <col style={{ width: '45%' }} />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell>{formConfig.inputs.airport.label}</TableCell>
            <TableCell></TableCell>
            <TableCell></TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <TextField
                value={formMessage?.airport || ''}
                onChange={getOnChange(inputs.airport.storeKey, setMsgPart)}
                error={!!errors?.airport}
                helperText={showError(errors?.airport)}
              />
            </TableCell>
            <TableCell colSpan={3}></TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              Scheduled Information:
            </TableCell>
            <TableCell colSpan={3}>
              <TextField
                multiline
                minRows={2}
                maxRows={2}
                value={formMessage?.si || ''}
                onChange={getOnChange(inputs.si.storeKey, setMsgPart)}
                error={!!errors?.si}
                helperText={showError(errors?.si)}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              General Information:
            </TableCell>
            <TableCell colSpan={3}>
              <TextField
                multiline
                minRows={2}
                maxRows={2}
                value={formMessage?.gi || ''}
                onChange={getOnChange(inputs.gi.storeKey, setMsgPart)}
                error={!!errors?.gi}
                helperText={showError(errors?.gi)}
              />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      <TableFormInnerBox>
        {DEBUG &&
          mockMessages.map((msg, i) => (
            <MockButton key={i} onClick={() => setMsgPart(msg)}>
              {i}
            </MockButton>
          ))}
      </TableFormInnerBox>
      <TableFormInnerBox>
        <FormButton onClick={handleAddMessage}>Add Message</FormButton>
        <FormButton
          variant="contained"
          color="warning"
          onClick={() => {
            clearFormMessage();
            // Reset uncontrolled inputs:
            const inputs = formRef.current?.querySelectorAll('input, textarea') || [];
            inputs.forEach((input) => ((input as HTMLInputElement).value = ''));
            clearFormMessageErrors();
          }}
        >
          Clear
        </FormButton>
      </TableFormInnerBox>
    </div>
  );
};
