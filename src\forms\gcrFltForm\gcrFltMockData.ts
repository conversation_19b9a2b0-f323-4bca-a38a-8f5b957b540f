import type { GcrFltArrLine, GcrFltDepLine, GcrFltLineFormInput, GcrFltMessage } from './gcrFltTypes';

const dummyArr: GcrFltArrLine = {
  action: 'N',
  operatorArr: 'KLM',
  flightNumberArr: '1103',
  date: '01JUN',
  seats: 189,
  aircraftType: 'HOPP',
  origin: 'CPHG',
  previous: 'AMST',
  timeArr: '1300',
  stArr: 'K',
  aircraftRegistration: 'OY-KAL',
};

const dummyDep: GcrFltDepLine = {
  action: 'N',
  operatorDep: 'AF',
  flightNumberDep: '1234',
  date: '01JUN',
  seats: 189,
  aircraftType: 'HOPP',
  timeDep: '1400',
  on: 0,
  next: 'FRAN',
  destination: 'LHRT',
  stDep: 'K',
  aircraftRegistration: 'OY-KAL',
};

export const mockLines: GcrFltLineFormInput[] = [
  // Arrival:
  { ...dummyArr },
  // Full of errors:
  {
    ...dummyArr,
    operatorArr: 'KL',
    flightNumberArr: '3',
    operatorDep: 'AF',
    flightNumberDep: '4',
    date: '01JUNE',
    origin: 'CP',
    previous: 'AMS',
    timeArr: '130',
    timeDep: '140',
    on: 9,
    next: 'RA',
    destination: 'LH',
  },
  // Departure:
  { ...dummyDep },
  // Change:
  {
    ...dummyArr,
    action: 'C',
    operatorArr: 'AF',
    flightNumberArr: '1234',
    seats: 220,
    aircraftType: '320M',
  },
  // Request:
  { ...dummyArr, action: 'R' },
];

const dummyMessage: GcrFltMessage = {
  airport: 'CPHG',
  si: 'STUFF',
  gi: 'MORE',
};

export const mockMessages: GcrFltMessage[] = [
  { ...dummyMessage },
  // Full of errors:
  {
    ...dummyMessage,
    airport: 'CP',
    si: '',
    gi: '',
  },
  // No errors:
  {
    ...dummyMessage,
    airport: 'AMST',
    si: 'STUFF',
    gi: 'MORE',
  },
];
