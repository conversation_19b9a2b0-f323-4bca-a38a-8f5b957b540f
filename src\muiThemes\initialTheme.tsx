// import { createTheme } from '@mui/material/styles';

// This theme is not used at the moment
/* export const theme = createTheme({
  components: {
    MuiTextField: {
      defaultProps: {
        size: 'small',
      },
    },
    MuiButton: {
      defaultProps: {
        size: 'small',
      },
    },
  },
  typography: {
    // fontSize: 11, // Default is 14
    h1: { fontSize: '2rem' }, // Default is 6rem
    h2: { fontSize: '1.5rem' }, // Default is 3.75rem
    h3: { fontSize: '1.2rem' }, // Default is 3rem
    h4: { fontSize: '1.1rem' }, // Default is 2.125rem
    h5: { fontSize: '1rem' }, // Default is 1.5rem
    h6: { fontSize: '0.9rem' }, // Default is 1.25rem
  },
  palette: {
    mode: 'light',
  },
}); */
