import Add from '@mui/icons-material/Add';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';

export type MessageOutputProps = {
  error: Record<string, string> | null;
  fullText: string | null;
  handleCopy: () => void;
};

/**
 * Displays the full message text and provides a button to copy it to the clipboard.
 * Agnostic to the message type and store etc, ie can be used for all three message types.
 * */
export const MessageOutput: React.FC<MessageOutputProps> = (props) => {
  const { error, fullText, handleCopy } = props;
  const theme = useTheme();

  return (
    <>
      <Paper elevation={0} sx={{ p: 0, overflow: 'auto', borderRadius: 0, mb: '.5rem' }}>
        <Box sx={{ backgroundColor: theme.palette.grey[200], p: '4px 6px' }}>
          <Typography variant="body1">Message</Typography>
        </Box>

        {error && (
          <Box sx={{ mb: 2, p: '0.5rem' }}>
            <Typography variant="h5">Error</Typography>
            {Object.keys(error).map((key) => (
              <Typography key={key} component="span" color="error" sx={{ display: 'block', mb: 0.5 }}>
                <Typography component="span" color="error">
                  {error[key]}
                </Typography>
              </Typography>
            ))}
          </Box>
        )}

        {/* Consider `whiteSpace: 'pre-wrap'` to wrap raw text */}
        {!error && fullText ? (
          <Box component="pre" sx={{ fontFamily: 'monospace', whiteSpace: 'pre', overflowX: 'auto', mb: 2 }}>
            {fullText}
          </Box>
        ) : (
          <Typography variant="body2" color="textSecondary">
            No message to output
          </Typography>
        )}
      </Paper>
      <Box textAlign="right">
        <Button startIcon={<Add />} variant="contained" color="primary" size="small" onClick={handleCopy}>
          Copy to Clipboard
        </Button>
      </Box>
    </>
  );
};
