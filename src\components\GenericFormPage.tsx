import Grid from '@mui/material/Grid';
import { PageSectionWrap } from './PageLayout';

type GenericFormPageProps /* <TLine, TMessage> */ = {
  // messageHeaderRenderer: React.ComponentType;
  messageHeaderForm: React.ReactNode;
  // lineFormContainer: React.ComponentType;
  lineForm: React.ReactNode;
  lineList: React.ReactNode;
  // messageFooterRenderer: React.ComponentType;
  messageFooterForm: React.ReactNode;
  // messageOutputContainer: React.ComponentType;
  messageOutput: React.ReactNode;
};

export const GenericFormPage = /* <TLine, TMessage> */ (props: GenericFormPageProps /* <TLine, TMessage> */) => {
  const {
    // messageHeaderRenderer: MessageHeaderRenderer,
    messageHeaderForm,
    // lineFormContainer: LineFormContainer,
    lineForm,
    lineList,
    // messageFooterRenderer: MessageFooter<PERSON>enderer,
    messageFooterForm,
    // messageOutputContainer: MessageOutputContainer,
    messageOutput,
  } = props;

  return (
    <>
      <PageSectionWrap>{messageHeaderForm}</PageSectionWrap>
      <PageSectionWrap>{lineForm}</PageSectionWrap>
      <PageSectionWrap>{lineList}</PageSectionWrap>
      <PageSectionWrap>
        <Grid container spacing={1} sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }}>
          <Grid size={5.8}>{messageFooterForm}</Grid>
          <Grid size={5.8}>{messageOutput}</Grid>
        </Grid>
      </PageSectionWrap>
    </>
  );
};
