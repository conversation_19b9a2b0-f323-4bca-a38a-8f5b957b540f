import { MessagePreview } from '@src/components/MessagePreview';
import { useMemo } from 'react';
import { formatGcrFltMsgFooter, formatGcrFltMsgHeader } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';

export const GcrFltMessagePreviewContainer: React.FC = () => {
  const { message, clearMessage } = useGcrFltStore();
  const msgHeader = useMemo(() => message && formatGcrFltMsgHeader(message), [message]);
  const msgFooter = useMemo(() => message && formatGcrFltMsgFooter(message), [message]);

  return <MessagePreview clearMessage={clearMessage} msgHeader={msgHeader} msgFooter={msgFooter} />;
};
