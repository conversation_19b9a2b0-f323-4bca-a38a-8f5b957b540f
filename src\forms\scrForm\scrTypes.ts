import type { LineAction, ScrServiceType } from '@src/validation/validationSchemas';

/** SCR line shared fields */
export type ScrSharedLineFields = {
  action: LineAction;
  dateFrom: string;
  dateTo: string;
  doop: string;
  seats: number;
  aircraftType: string;
  frequency?: string;
  aircraftRegistration?: string;
};
/** SCR line arrival fields */
export type ScrArrLineFields = {
  operatorArr: string;
  flightNumberArr: string;
  origin: string;
  previous?: string;
  timeArr: string;
  stArr: ScrServiceType;
};
/** SCR line departure fields */
export type ScrDepLineFields = {
  operatorDep: string;
  flightNumberDep: string;
  timeDep: string;
  on?: number;
  next?: string;
  destination: string;
  stDep: ScrServiceType;
};

/** SCR arrival line type */
export type ScrArrLine = ScrArrLineFields & ScrSharedLineFields;
/** SCR departure line type */
export type ScrDepLine = ScrDepLineFields & ScrSharedLineFields;

/** SCR line form validation */
export type ScrLine = ScrSharedLineFields & ScrArrLineFields & ScrDepLineFields;
/** SCR line form input */
export type ScrLineFormInput = Partial<ScrLine>;

/** For message form validation */
export type ScrMessage = {
  /** Season code, S or W followed by year (e.g. W25) */
  season: string;
  /** Message date in DDMMM format (e.g. 20MAY) */
  date: string;
  /** Coordinated airport IATA code (e.g. CPH) */
  airport: string;
  /** Creator email */
  creator?: string;
  /* Scheduled information */
  si?: string;
  /** General information */
  gi?: string;
};

/** For full message validation */
export type FullScrMessage = {
  message: ScrMessage;
  lines: ScrLine[];
};
