import type { FormConfig, FormInputEvent } from '@src/typesGlobal';
import { useCallback, useMemo } from 'react';
import { GcrRegMessageFormTableRenderer } from './GcrRegMessageFormTableRenderer';
import { useGcrRegStore } from './gcrRegStore';
import type { GcrRegMessage } from './gcrRegTypes';

/**
 * The 'GcrFltMessageFormContainer' creates config and handlers for the form inputs
 * and passes them to the 'ScrMessageFormTableRenderer' component.
 */
export const GcrRegMessageFormContainer: React.FC = () => {
  const { setFormMessagePartial } = useGcrRegStore();

  /**
   * 'formConfig' is memoized to prevent unnecessary re-renders.
   * It contains the configuration for the form inputs.
   */
  const formConfig: FormConfig<GcrRegMessage> = useMemo(
    () => ({
      inputs: {
        season: {
          storeKey: 'season',
          label: 'Season',
          type: 'text',
          toUpperCase: true,
        },
        date: {
          storeKey: 'date',
          label: 'Date',
          type: 'text',
          toUpperCase: true,
        },
        airport: {
          storeKey: 'airport',
          label: 'Airport',
          type: 'text',
          toUpperCase: true,
        },
        creator: {
          storeKey: 'creator',
          label: 'Creator',
          type: 'text',
        },
        si: {
          storeKey: 'si',
          label: 'SI',
          type: 'text',
        },
        gi: {
          storeKey: 'gi',
          label: 'GI',
          type: 'text',
        },
      },
    }),
    [],
  );

  /**
   * 'getOnChange' will given a field key, return a function that takes an event
   * and updates the store for that field.
   * Memoize it to prevent unnecessary re-renders.
   */
  const getOnChange = useCallback(
    (key: keyof GcrRegMessage) => (e: FormInputEvent) => {
      let value: GcrRegMessage[keyof GcrRegMessage] = e.target.value;
      // Check if this field should be uppercased
      if (formConfig.inputs[key]?.toUpperCase) {
        value = e.target.value?.toUpperCase();
      }
      // Check if this field should be a number type
      // if (formConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
      //   value = Number(e.target.value);
      // }
      setFormMessagePartial({ [key]: value });
    },
    [formConfig.inputs, setFormMessagePartial],
  );

  return <GcrRegMessageFormTableRenderer formConfig={formConfig} getOnChange={getOnChange} />;
};
