# Form Store Factory

This module provides a factory function to create Zustand stores that implement the `BaseFormStore` interface. It eliminates code duplication between similar form stores like `useScrStore` and `useGcrFltStore`.

## Usage

### Basic Usage

```typescript
import { createFormStore, createEmptyMessageWithDate } from '@src/utils/createFormStore';
import type { YourLine, YourLineFormInput, YourMessage } from './types';

// Define your empty line and message objects
const emptyLine: YourLineFormInput = { action: 'N' };
const emptyMessage: Partial<YourMessage> = createEmptyMessageWithDate();

// Create the store
export const useYourStore = createFormStore<YourLine, YourMessage, YourLineFormInput>({
  emptyLine,
  emptyMessage,
});
```

### Type Parameters

- `TLine`: The complete line type (e.g., `ScrLine`, `GcrFltLine`)
- `TMessage`: The complete message type (e.g., `ScrMessage`, `GcrFltMessage`)

### Configuration Object

The `createFormStore` function accepts a configuration object with:

- `emptyLine`: An object representing an empty line for form initialization
- `emptyMessage`: A partial message object for form initialization

### Helper Functions

#### `createEmptyMessageWithDate()`

Creates an object with a `date` property formatted as `DDMMM` (e.g., "18JUN") representing today's date. This is the standard format used across the application.

## Generated Store Interface

The created store implements the full `BaseFormStore<TLine, TMessage>` interface, providing:

### Message Management

- `message`: Current message state
- `setMessage(message)`: Set the current message
- `clearMessage()`: Clear the current message

### Message Form Management

- `formMessage`: Form message state
- `setFormMessagePartial(updates)`: Update form message partially
- `clearFormMessage()`: Reset form message to empty state

### Message Error Management

- `formMessageErrors`: Error state for message form
- `setFormMessageErrors(errors)`: Set message form errors
- `clearFormMessageErrors()`: Clear message form errors

### Lines Management

- `lines`: Array of lines
- `addLine(line)`: Add a new line
- `updateLine(updates)`: Update the currently selected line
- `deleteLine(index)`: Delete a line by index
- `clearAllLines()`: Clear all lines

### Line Form Management

- `formLine`: Current line being edited
- `formLineIndex`: Index of the line being edited
- `setFormLine(index?)`: Set the line to edit (null for new line)
- `setFormLinePartial(updates)`: Update form line partially
- `clearFormLine()`: Clear form line and reset state

### Line Error Management

- `formLineErrors`: Error state for line form
- `setFormLineErrors(errors)`: Set line form errors
- `clearFormLineErrors()`: Clear line form errors

## Migration from Existing Stores

To migrate an existing store to use the factory:

1. Import the factory and helper functions
2. Define your `emptyLine` and `emptyMessage` objects
3. Replace the entire store implementation with a single `createFormStore` call
4. Remove the manual Zustand and immer imports

### Before (98 lines)

```typescript
import type { BaseFormStore } from '@src/typesGlobal';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
// ... 95 more lines of boilerplate
```

### After (10 lines)

```typescript
import { createFormStore, createEmptyMessageWithDate } from '@src/utils/createFormStore';
import type { ScrLine, ScrLineFormInput, ScrMessage } from './types';

const emptyLine: ScrLineFormInput = { action: 'N' };
const emptyMessage: Partial<ScrMessage> = createEmptyMessageWithDate();

export const useScrStore = createFormStore<ScrLine, ScrMessage, ScrLineFormInput>({
  emptyLine,
  emptyMessage,
});
```

## Benefits

- **Reduced Code Duplication**: Eliminates ~88 lines of identical boilerplate per store
- **Consistency**: Ensures all form stores behave identically
- **Maintainability**: Changes to store behavior only need to be made in one place
- **Type Safety**: Full TypeScript support with proper type inference
- **Easy Testing**: Factory can be easily unit tested once, benefiting all stores
