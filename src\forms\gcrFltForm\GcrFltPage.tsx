import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { PageSectionWrap } from '@src/components/PageLayout';
import { GcrFltLineFormContainer } from './GcrFltLineFormContainer';
import { GcrFltLineListContainer } from './GcrFltLineListContainer';
import { GcrFltMessageFormFooterRenderer } from './GcrFltMessageFormFooterRenderer';
import { GcrFltMessageFormHeaderRenderer } from './GcrFltMessageFormHeaderRenderer';
import { GcrFltMessageOutputContainer } from './GcrFltMessageOutputContainer';
import { GcrFltMessagePreviewContainer } from './GcrFltMessagePreviewContainer';

export const GcrFltPage: React.FC = () => {
  return (
    <>
      <Box sx={{ height: '1.5rem' }} />
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap /* sx={{ maxWidth: '50rem' }} */>
          <GcrFltMessageFormHeaderRenderer />
        </PageSectionWrap>
      </Box>
      <Grid container spacing={1} sx={{ alignItems: 'flex-start' }}>
        <Grid sx={{ mr: '1rem', width: '40%', minWidth: '90rem', maxWidth: '100rem' }}>
          <PageSectionWrap /* sx={{ maxWidth: '99%' }} */>
            <GcrFltLineFormContainer />
          </PageSectionWrap>
        </Grid>
        <Grid sx={{ minWidth: '30rem', maxWidth: '40rem', width: '100%' /* , mt: '-0.75rem' */ }}>
          <PageSectionWrap>
            <GcrFltLineListContainer />
          </PageSectionWrap>
        </Grid>
      </Grid>
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap /* sx={{ maxWidth: '50rem' }} */>
          <GcrFltMessageFormFooterRenderer />
        </PageSectionWrap>
        <PageSectionWrap>
          <GcrFltMessagePreviewContainer />
        </PageSectionWrap>
        <PageSectionWrap>
          <GcrFltMessageOutputContainer />
        </PageSectionWrap>
      </Box>
    </>
  );
};
