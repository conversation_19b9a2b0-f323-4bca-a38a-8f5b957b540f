import Grid from '@mui/material/Grid';
import { LineList } from '@src/components/LineList';
import { PageSectionWrap } from '@src/components/PageLayout';
import { GcrFltLineFormContainer } from './GcrFltLineFormContainer';
import { GcrFltMessageFormFooterRenderer } from './GcrFltMessageFormFooterRenderer';
import { GcrFltMessageFormHeaderRenderer } from './GcrFltMessageFormHeaderRenderer';
import { GcrFltMessageOutputContainer } from './GcrFltMessageOutputContainer';
import { formatGcrFltLine } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';

export const GcrFltPage: React.FC = () => {
  return (
    <>
      <PageSectionWrap>
        <GcrFltMessageFormHeaderRenderer />
      </PageSectionWrap>
      <PageSectionWrap>
        <GcrFltLineFormContainer />
      </PageSectionWrap>
      <PageSectionWrap>
        <LineList useStore={useGcrFltStore} formatLine={formatGcrFltLine} />
      </PageSectionWrap>
      <PageSectionWrap>
        <Grid container spacing={1} sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }}>
          <Grid size={5.8}>
            <GcrFltMessageFormFooterRenderer />
          </Grid>
          <Grid size={5.8}>
            <GcrFltMessageOutputContainer />
          </Grid>
        </Grid>
      </PageSectionWrap>
    </>
  );
};
