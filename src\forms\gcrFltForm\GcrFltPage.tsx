import { GenericFormPage } from '@src/components/GenericFormPage';
import { LineList } from '@src/components/LineList';
import { GcrFltLineFormContainer } from './GcrFltLineFormContainer';
import { GcrFltMessageFormFooterRenderer } from './GcrFltMessageFormFooterRenderer';
import { GcrFltMessageFormHeaderRenderer } from './GcrFltMessageFormHeaderRenderer';
import { GcrFltMessageOutputContainer } from './GcrFltMessageOutputContainer';
import { formatGcrFltLine } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';

export const GcrFltPage: React.FC = () => {
  return (
    <GenericFormPage
      messageHeaderForm={<GcrFltMessageFormHeaderRenderer />}
      lineForm={<GcrFltLineFormContainer />}
      lineList={<LineList useStore={useGcrFltStore} formatLine={formatGcrFltLine} />}
      messageFooterForm={<GcrFltMessageFormFooterRenderer />}
      messageOutput={<GcrFltMessageOutputContainer />}
    />
  );
};
