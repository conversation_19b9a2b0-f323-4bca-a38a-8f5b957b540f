import { isNil } from 'lodash';
import type { GcrRegLine, GcrRegMessage } from './gcrRegTypes';

const val2str = <T = unknown>(val: T): string => (isNil(val) ? '' : String(val));

export const formatGcrRegLine = (line: GcrRegLine): string => {
  const action = val2str(line.action);
  const date = val2str(line.date);
  const seats = val2str(line.seats?.toString().padStart(3, '0'));
  const aircraftType = val2str(line.aircraftType);
  const origin = val2str(line.origin);
  const previous = val2str(line.previous);
  const timeArr = val2str(line.timeArr);
  const timeDep = val2str(line.timeDep);
  const on = line?.on === 0 ? '' : val2str(line?.on);
  const next = val2str(line.next);
  const destination = val2str(line.destination);
  const stArr = val2str(line.stArr);
  const stDep = val2str(line.stDep);
  const aircraftRegistration = val2str(line.aircraftRegistration);

  // Whether there is an arrival and departure:
  const isArr = origin || previous || timeArr || stArr;
  const isDep = timeDep || on || next || destination || stDep;
  // Create the arrival and departure strings:
  const arr1 = isArr ? ` ${origin}${previous}${timeArr} ` : '';
  const dep1 = isDep ? ` ${timeDep}${on}${next}${destination} ` : '';
  // If there is a departure but no arrival, we need a gap:
  const gap = isDep && !isArr ? ' ' : '';

  let result =
    `${action}${gap}${aircraftRegistration}` +
    ` ${date} ${seats}${aircraftType}` +
    `${arr1}${dep1}` +
    `${stArr}${stDep}`;

  // Remove double spaces
  result = result.replace(/ {2,}/g, ' ').trim();

  // console.log({ result });
  return result;
};

export const formatGcrRegMsgHeader = (msg: GcrRegMessage): string => {
  return `GCR\n/REG\n${msg.airport}`;
};

export const formatGcrRegMsgFooter = (msg: GcrRegMessage): string => {
  return `SI ${msg.si}\nGI ${msg.gi}`;
};

export const formatGcrRegMessage = (msg: GcrRegMessage, lines: GcrRegLine[]): string => {
  const header = formatGcrRegMsgHeader(msg);
  const linesAsStrings = lines.map(formatGcrRegLine);
  const footer = formatGcrRegMsgFooter(msg);
  return [header, ...linesAsStrings, footer].join('\n');
};
