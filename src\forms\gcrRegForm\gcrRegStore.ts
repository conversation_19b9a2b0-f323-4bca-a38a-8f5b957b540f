import { createFormStore, type FormStore } from '@src/formStore/createFormStore';
import type { GcrRegLine, GcrRegLineFormInput, GcrRegMessage } from './gcrRegTypes';

const emptyLine: GcrRegLineFormInput = { action: 'N' };
const emptyMessage: Partial<GcrRegMessage> = {};

export const useGcrRegStore: FormStore<GcrRegLine, GcrRegMessage> = createFormStore<GcrRegLine, GcrRegMessage>({
  emptyLine,
  emptyMessage,
});
