import { createTheme } from '@mui/material/styles';
import { compactGridThemePallette } from './compactGridThemePallette';

export const compactGridTheme = createTheme({
  spacing: 1, // Even tighter spacing than dense theme

  palette: compactGridThemePallette,

  typography: {
    fontSize: 11, // Smaller base font size
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: { fontSize: '1.5rem', fontWeight: 500 },
    h2: { fontSize: '1.25rem', fontWeight: 500 },
    h3: { fontSize: '1.1rem', fontWeight: 500 },
    h4: { fontSize: '1rem', fontWeight: 500 },
    h5: { fontSize: '0.9rem', fontWeight: 500 },
    h6: { fontSize: '0.8rem', fontWeight: 500 },
    body1: { fontSize: '0.75rem', lineHeight: 1.2 },
    body2: { fontSize: '0.7rem', lineHeight: 1.2 },
    caption: { fontSize: '0.65rem', lineHeight: 1.1 },
  },

  components: {
    // Global container styling
    MuiContainer: {
      styleOverrides: {
        root: {
          padding: '4px !important',
          margin: 0,
        },
      },
    },

    // Paper components for grid sections
    MuiPaper: {
      defaultProps: {
        elevation: 0,
      },
      styleOverrides: {
        root: ({ theme }) => ({
          border: `1px solid ${theme.palette.grey[300]}`,
          borderRadius: 4,
          margin: '2px',
          padding: '4px',
          backgroundColor: '#ffffff',
          boxShadow: 'none', // This removes the shadow
        }),
      },
    },

    // Text fields - very compact
    MuiTextField: {
      defaultProps: {
        fullWidth: true,
        size: 'small',
        variant: 'outlined',
      },
      styleOverrides: {
        root: {
          margin: '1px',
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#ffffff',
            // border: '1px solid #d0d0d0',
            borderRadius: 2,
            fontSize: '0.7rem',
            minHeight: '20px',
            '&:hover': {
              borderColor: '#1976d2',
            },
            '&.Mui-focused': {
              borderColor: '#1976d2',
              boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.2)',
            },
          },
        },
      },
    },

    // Input base - minimal padding
    MuiInputBase: {
      styleOverrides: {
        root: {
          fontSize: '0.7rem',
          lineHeight: 1.2,
        },
        input: {
          padding: '2px 4px !important',
          fontSize: '0.7rem',
          height: '16px',
          minHeight: 'unset',
        },
        multiline: {
          padding: '2px 4px',
        },
      },
    },

    // Form controls
    MuiFormControl: {
      styleOverrides: {
        root: {
          margin: '1px',
          minWidth: 'unset',
        },
      },
    },

    // Buttons - compact and styled
    MuiButton: {
      defaultProps: {
        size: 'small',
        variant: 'contained',
      },
      styleOverrides: {
        root: {
          minHeight: '22px',
          padding: '2px 8px',
          fontSize: '0.7rem',
          fontWeight: 500,
          textTransform: 'none',
          borderRadius: 4,
          margin: '1px',
        },
        // Primary color (default blue)
        containedPrimary: {
          backgroundColor: 'primary.main',
          color: 'primary.contrastText',
          boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
          '&:hover': {
            // backgroundColor: '#1e5a8a',
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
          },
        },
        // Secondary color (grey)
        containedSecondary: {
          backgroundColor: 'secondary.main',
          color: 'secondary.contrastText',
          boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
          '&:hover': {
            // backgroundColor: 'secondary.dark',
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
          },
        },
        // Warning color (orange)
        containedWarning: {
          backgroundColor: 'warning.main',
          color: 'warning.contrastText',
          boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
          '&:hover': {
            // backgroundColor: '#c55502',
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
          },
        },
        // Error color (red)
        containedError: {
          backgroundColor: 'error.main',
          color: 'error.contrastText',
          boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
          '&:hover': {
            // backgroundColor: '#b71c1c',
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
          },
        },
        // Success color (green)
        containedSuccess: {
          backgroundColor: 'success.main',
          color: 'success.contrastText',
          boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
          '&:hover': {
            // backgroundColor: '#1b5e20',
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
          },
        },
        // Outlined variants
        outlinedPrimary: {
          borderColor: 'primary.main',
          color: 'primary.main',
          backgroundColor: 'white.50',
          '&:hover': {
            // backgroundColor: '#f3f8ff',
            // borderColor: '#1e5a8a',
          },
        },
        outlinedWarning: {
          borderColor: 'warning.main',
          color: 'warning.main',
          backgroundColor: 'white.50',
          '&:hover': {
            // backgroundColor: '#fff8f0',
            // borderColor: '#c55502',
          },
        },
        outlinedError: {
          borderColor: 'error.main',
          color: 'error.main',
          backgroundColor: 'white.50',
          '&:hover': {
            // backgroundColor: '#ffebee',
            // borderColor: '#b71c1c',
          },
        },
        outlinedSuccess: {
          borderColor: 'success.main',
          color: 'success.main',
          backgroundColor: 'white.50',
          '&:hover': {
            // backgroundColor: '#f1f8e9',
            // borderColor: '#1b5e20',
          },
        },
      },
    },

    // Table styling - compact with grey headers
    MuiTable: {
      styleOverrides: {
        root: {
          // border: '1px solid #e0e0e0',
          // borderCollapse: 'separate',
          // borderSpacing: 0,
        },
      },
    },

    MuiTableHead: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderTop: `1px solid ${theme.palette.grey[300]}`,
          height: '1.5rem',
        }),
      },
    },

    MuiTableCell: {
      styleOverrides: {
        root: ({ theme }) => ({
          padding: '2px 4px',
          fontSize: '0.7rem',
          lineHeight: 1.2,
          border: `1px solid ${theme.palette.grey[300]}`,
          borderTop: 'none',
          borderLeft: 'none',
          '&:first-of-type': {
            borderLeft: `1px solid ${theme.palette.grey[300]}`,
          },
        }),
        head: ({ theme }) => ({
          backgroundColor: theme.palette.grey[200],
          color: theme.palette.grey[900],
          fontWeight: 600,
          fontSize: '0.7rem',
          textAlign: 'center',
          padding: '4px 6px',
        }),
        body: ({ theme }) => ({
          backgroundColor: theme.palette.grey[100],
        }),
      },
    },

    MuiTableRow: {
      styleOverrides: {
        root: {
          // '&:hover': {
          //   backgroundColor: '#f0f7ff !important',
          // },
        },
      },
    },

    // Grid system
    MuiGrid: {
      styleOverrides: {
        root: {
          margin: '1px',
          padding: '1px',
          // Apply padding to grid items (when they have the item prop)
          '&.MuiGrid-item': {
            padding: '2px !important',
          },
        },
        container: {
          margin: '0 !important',
          width: '100% !important',
        },
      },
    },

    // Typography adjustments
    MuiTypography: {
      styleOverrides: {
        root: {
          lineHeight: 1.2,
        },
        h6: {
          fontSize: '0.8rem',
          fontWeight: 600,
          // color: '#1976d2',
          margin: '2px 0',
        },
      },
    },

    // Form labels
    MuiFormLabel: {
      styleOverrides: {
        root: ({ theme }) => ({
          fontSize: '0.7rem',
          color: theme.palette.grey[500],
          fontWeight: 500,
        }),
      },
    },

    // Select components
    MuiSelect: {
      styleOverrides: {
        select: {
          padding: '2px 4px !important',
          fontSize: '0.7rem',
          minHeight: '16px',
        },
      },
    },

    // Tabs for navigation
    // MuiTabs: {
    //   styleOverrides: {
    //     root: {
    //       backgroundColor: '#1976d2',
    //       minHeight: '32px',
    //       '& .MuiTab-root': {
    //         minHeight: '32px',
    //         fontSize: '0.75rem',
    //         fontWeight: 500,
    //         color: '#ffffff',
    //         textTransform: 'none',
    //         '&.Mui-selected': {
    //           backgroundColor: '#1565c0',
    //           color: '#ffffff',
    //         },
    //       },
    //     },
    //   },
    // },
  },
});
