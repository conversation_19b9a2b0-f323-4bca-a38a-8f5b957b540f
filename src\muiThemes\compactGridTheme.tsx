import { createTheme } from '@mui/material/styles';

export const compactGridTheme = createTheme({
  spacing: 1, // Even tighter spacing than dense theme
  
  palette: {
    primary: {
      main: '#1976d2', // Blue for headers and primary elements
      light: '#42a5f5',
      dark: '#1565c0',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#f5f5f5', // Light grey for backgrounds
      light: '#fafafa',
      dark: '#e0e0e0',
    },
    background: {
      default: '#f8f9fa', // Very light grey background
      paper: '#ffffff',
    },
    grey: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#eeeeee',
      300: '#e0e0e0',
      400: '#bdbdbd',
      500: '#9e9e9e',
    },
  },
  
  typography: {
    fontSize: 11, // Smaller base font size
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: { fontSize: '1.5rem', fontWeight: 500 },
    h2: { fontSize: '1.25rem', fontWeight: 500 },
    h3: { fontSize: '1.1rem', fontWeight: 500 },
    h4: { fontSize: '1rem', fontWeight: 500 },
    h5: { fontSize: '0.9rem', fontWeight: 500 },
    h6: { fontSize: '0.8rem', fontWeight: 500 },
    body1: { fontSize: '0.75rem', lineHeight: 1.2 },
    body2: { fontSize: '0.7rem', lineHeight: 1.2 },
    caption: { fontSize: '0.65rem', lineHeight: 1.1 },
  },
  
  components: {
    // Global container styling
    MuiContainer: {
      styleOverrides: {
        root: {
          padding: '4px !important',
          margin: 0,
        },
      },
    },
    
    // Paper components for grid sections
    MuiPaper: {
      styleOverrides: {
        root: {
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          margin: '2px',
          padding: '4px',
          backgroundColor: '#ffffff',
        },
      },
    },
    
    // Text fields - very compact
    MuiTextField: {
      defaultProps: {
        fullWidth: true,
        size: 'small',
        variant: 'outlined',
      },
      styleOverrides: {
        root: {
          margin: '1px',
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#ffffff',
            border: '1px solid #d0d0d0',
            borderRadius: 2,
            fontSize: '0.7rem',
            minHeight: '20px',
            '&:hover': {
              borderColor: '#1976d2',
            },
            '&.Mui-focused': {
              borderColor: '#1976d2',
              boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.2)',
            },
          },
        },
      },
    },
    
    // Input base - minimal padding
    MuiInputBase: {
      styleOverrides: {
        root: {
          fontSize: '0.7rem',
          lineHeight: 1.2,
        },
        input: {
          padding: '2px 4px !important',
          fontSize: '0.7rem',
          height: '16px',
          minHeight: 'unset',
        },
        multiline: {
          padding: '2px 4px',
        },
      },
    },
    
    // Form controls
    MuiFormControl: {
      styleOverrides: {
        root: {
          margin: '1px',
          minWidth: 'unset',
        },
      },
    },
    
    // Buttons - compact and styled
    MuiButton: {
      defaultProps: {
        size: 'small',
        variant: 'contained',
      },
      styleOverrides: {
        root: {
          minHeight: '22px',
          padding: '2px 8px',
          fontSize: '0.7rem',
          fontWeight: 500,
          textTransform: 'none',
          borderRadius: 2,
          margin: '1px',
        },
        contained: {
          backgroundColor: '#1976d2',
          color: '#ffffff',
          boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
          '&:hover': {
            backgroundColor: '#1565c0',
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
          },
        },
        outlined: {
          borderColor: '#1976d2',
          color: '#1976d2',
          backgroundColor: '#ffffff',
          '&:hover': {
            backgroundColor: '#f3f8ff',
            borderColor: '#1565c0',
          },
        },
      },
    },
    
    // Table styling - compact with blue headers
    MuiTable: {
      styleOverrides: {
        root: {
          border: '1px solid #e0e0e0',
          borderCollapse: 'separate',
          borderSpacing: 0,
        },
      },
    },
    
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: '#1976d2',
        },
      },
    },
    
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: '2px 4px',
          fontSize: '0.7rem',
          lineHeight: 1.2,
          border: '1px solid #e0e0e0',
          borderTop: 'none',
          borderLeft: 'none',
          '&:first-of-type': {
            borderLeft: '1px solid #e0e0e0',
          },
        },
        head: {
          backgroundColor: '#1976d2',
          color: '#ffffff',
          fontWeight: 600,
          fontSize: '0.7rem',
          textAlign: 'center',
          padding: '4px 6px',
          borderColor: '#1565c0',
        },
        body: {
          backgroundColor: '#ffffff',
          '&:nth-of-type(even)': {
            backgroundColor: '#fafafa',
          },
        },
      },
    },
    
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: '#f0f7ff !important',
          },
        },
      },
    },
    
    // Grid system
    MuiGrid: {
      styleOverrides: {
        root: {
          margin: '1px',
          padding: '1px',
        },
        container: {
          margin: '0 !important',
          width: '100% !important',
        },
        item: {
          padding: '2px !important',
        },
      },
    },
    
    // Typography adjustments
    MuiTypography: {
      styleOverrides: {
        root: {
          lineHeight: 1.2,
        },
        h6: {
          fontSize: '0.8rem',
          fontWeight: 600,
          color: '#1976d2',
          margin: '2px 0',
        },
      },
    },
    
    // Form labels
    MuiFormLabel: {
      styleOverrides: {
        root: {
          fontSize: '0.7rem',
          color: '#666666',
          fontWeight: 500,
        },
      },
    },
    
    // Select components
    MuiSelect: {
      styleOverrides: {
        select: {
          padding: '2px 4px !important',
          fontSize: '0.7rem',
          minHeight: '16px',
        },
      },
    },
    
    // Tabs for navigation
    MuiTabs: {
      styleOverrides: {
        root: {
          backgroundColor: '#1976d2',
          minHeight: '32px',
          '& .MuiTab-root': {
            minHeight: '32px',
            fontSize: '0.75rem',
            fontWeight: 500,
            color: '#ffffff',
            textTransform: 'none',
            '&.Mui-selected': {
              backgroundColor: '#1565c0',
              color: '#ffffff',
            },
          },
        },
      },
    },
  },
});
