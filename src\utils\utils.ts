import { type ZodError } from 'zod';

export const zodErrorMap = (error: ZodError): Record<string, string> => {
  const map: Record<string, string> = {};

  error.issues.forEach((issue, index) => {
    // console.log(`zodErrorMap issue`, issue);
    const path = issue.path[0] || `Issue ${index + 1}`;
    map[path] = issue.message;
  });

  return map;
};

/**
 * Converts a DOOP shorthand to a 7 character format, padding with zeros.
 */
export const formatDoop = (doop: string): string => {
  const digits = new Set(doop.split(''));
  return Array.from({ length: 7 }, (_, i) => {
    const day = (i + 1).toString();
    return digits.has(day) ? day : '0';
  }).join('');
};
