import type { FormConfig } from '@src/typesGlobal';
import type { GcrFltLine } from './gcrFltTypes';
import { lineActionValues, serviceTypeValues } from '@src/validation/validationSchemas';

export const gcrFltLineFormConfig: FormConfig<GcrFltLine> = {
  inputs: {
    action: {
      storeKey: 'action',
      label: '',
      type: 'select',
      options: lineActionValues,
      tabOrder: 1,
    },
    operatorArr: {
      storeKey: 'operatorArr',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 2,
    },
    flightNumberArr: {
      storeKey: 'flightNumberArr',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 3,
    },
    operatorDep: {
      storeKey: 'operatorDep',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 4,
    },
    flightNumberDep: {
      storeKey: 'flightNumberDep',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 5,
    },
    date: {
      storeKey: 'date',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 6,
    },
    seats: {
      storeKey: 'seats',
      label: '',
      type: 'number',
      tabOrder: 7,
    },
    aircraftType: {
      storeKey: 'aircraftType',
      label: '',
      type: 'text',
      tabOrder: 8,
    },
    origin: {
      storeKey: 'origin',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 9,
    },
    previous: {
      storeKey: 'previous',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 10,
    },
    timeArr: {
      storeKey: 'timeArr',
      label: '',
      type: 'text',
      tabOrder: 11,
    },
    timeDep: {
      storeKey: 'timeDep',
      label: '',
      type: 'text',
      tabOrder: 12,
    },
    on: {
      storeKey: 'on',
      label: '',
      type: 'number',
      tabOrder: 13,
    },
    next: {
      storeKey: 'next',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 14,
    },
    destination: {
      storeKey: 'destination',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 15,
    },
    stArr: {
      storeKey: 'stArr',
      label: '',
      type: 'select',
      options: serviceTypeValues,
      tabOrder: 16,
    },
    stDep: {
      storeKey: 'stDep',
      label: '',
      type: 'select',
      options: serviceTypeValues,
      tabOrder: 17,
    },
    aircraftRegistration: {
      storeKey: 'aircraftRegistration',
      type: 'text',
      label: '',
      toUpperCase: true,
      tabOrder: 18,
    },
  },
};
