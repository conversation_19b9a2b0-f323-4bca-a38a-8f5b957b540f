import { MessagePreview } from '@src/components/MessagePreview';
import { useMemo } from 'react';
import { useGcrRegStore } from './gcrRegStore';
import { formatGcrRegMsgHeader, formatGcrRegMsgFooter } from './gcrRegUtils';

export const GcrRegMessagePreviewContainer: React.FC = () => {
  const { message, clearMessage } = useGcrRegStore();
  const msgHeader = useMemo(() => message && formatGcrRegMsgHeader(message), [message]);
  const msgFooter = useMemo(() => message && formatGcrRegMsgFooter(message), [message]);

  return <MessagePreview clearMessage={clearMessage} msgHeader={msgHeader} msgFooter={msgFooter} />;
};
