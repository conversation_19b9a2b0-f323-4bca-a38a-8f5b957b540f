import { validateActionSequence } from '@src/validation/actionSequenceValidation';
import { z } from 'zod';
import { gcrFltLineSchema, gcrFltMessageSchema } from './gcrFltValidation';

export const fullGcrFltMessageSchema = z
  .object({
    message: gcrFltMessageSchema, // header/footer
    lines: z.array(gcrFltLineSchema), // validated lines
  })
  .superRefine((data, ctx) => {
    const { lines } = data;

    // Validate C/R action sequence rules
    validateActionSequence(lines, ctx);
  });
