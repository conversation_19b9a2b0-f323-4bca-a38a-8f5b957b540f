import z from 'zod';

/** Schema for operator, 2-3 characters or blank */
export const operatorSchema = z.string().regex(/^([A-Z]{2,3})?$/, { message: '2-3 characters or blank' });

/** Schema for date format DDMMM */
export const dateSchema = z.string().regex(/^\d{2}[A-Z]{3}$/, { message: 'DDMMM format' });

/** Schema for aircraft registration, 3-7 characters, first 1-2 letters, then optional dash, then 3-5 letters/numbers */
export const aircraftRegSchema = z.string().regex(/^[A-Z]{1,2}-?[A-Z0-9]{3,5}$/, { message: 'A(A)(-)###(##) format' });

/** Schema for time format HHMM, 00-23 hours, 00-59 minutes */
export const timeSchema = z.string().superRefine((val, ctx) => {
  if (!val.match(/^\d{4}$/)) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'HHMM format' });
    return;
  }
  const hours = parseInt(val.substring(0, 2));
  const minutes = parseInt(val.substring(2, 4));

  if (hours < 0 || hours > 23) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: '00-23 hours only' });
  }
  if (minutes < 0 || minutes > 59) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: '00-59 minutes only' });
  }
});

/** Schema for 3 characters, not blank */
export const schema3charsNotBlank = z.string().length(3, { message: '3 characters' });
/** Schema for 4 characters, not blank */
export const schema4charsNotBlank = z.string().length(4, { message: '4 characters' });

export const schema2to4charsNotBlank = z
  .string()
  .min(2, { message: '2-4 characters' })
  .max(4, { message: '2-4 characters' });

/** Schema for 3 characters, blank allowed */
export const schema3charsOrBlank = z
  .string()
  .length(3, { message: '3 characters or blank' })
  .or(z.string().regex(/^$/));
/** Schema for 4 characters, blank allowed */
export const schema4charsOrBlank = z
  .string()
  .length(4, { message: '4 characters or blank' })
  .or(z.string().regex(/^$/));

/** Schema for flight number, 1-4 digits with optional A-Z suffix */
export const flightNumberSchema = z
  .string()
  .regex(/^\d{1,4}[A-Z]?$/, { message: '1-4 digits with optional A-Z suffix' });

/** Generic helper to create both enum type and schema */
function createEnum<T extends readonly [string, ...string[]]>(values: T) {
  return {
    values,
    schema: z.enum(values),
    type: {} as T[number],
  };
}

// Use the helper
export const LineActionEnum = createEnum(['C', 'R', 'N', 'D'] as const);
export type LineAction = typeof LineActionEnum.type;
export const lineActionSchema = LineActionEnum.schema;
export const lineActionValues = LineActionEnum.values;

// prettier-ignore
export const scrServiceTypeValues = ['', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X'] as const;
const ScrServiceTypeEnum = createEnum(scrServiceTypeValues);
export type ScrServiceType = typeof ScrServiceTypeEnum.type;
export const scrServiceTypeSchema = ScrServiceTypeEnum.schema;

// prettier-ignore
export const gcrServiceTypeValues = ['', 'D', 'E', 'I', 'N', 'P', 'U', 'W' ] as const;
const GcrServiceTypeEnum = createEnum(gcrServiceTypeValues);
export type GcrServiceType = typeof GcrServiceTypeEnum.type;
export const gcrServiceTypeSchema = GcrServiceTypeEnum.schema;

// TODO: doopSchemaShorthand currently not used
/** Schema for DOOP shorthand format, 1-7 digits, ascending order, no duplicates */
export const doopSchemaShorthand = z.string().superRefine((val, ctx) => {
  // Check for empty string:
  if (!val || val.trim() === '') {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'Required' });
    return;
  }
  // Check for 1-7 only:
  if (!/^[1-7]+$/.test(val)) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: '1-7 only allowed' });
    return;
  }
  // Check for ascending order and no duplicates:
  const correct = '1234567';
  let lastIndex = -1;
  for (const char of val) {
    const index = correct.indexOf(char);
    if (index <= lastIndex) {
      ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'Ascending order (1-7), no duplicates' });
      return;
    }
    lastIndex = index;
  }
});

/** Schema for DOOP full format, 7 characters, 0-7 only, each position contains either 0 or the correct day number */
export const doopSchemaFull = z.string().superRefine((val, ctx) => {
  // Check for empty string:
  if (!val || val.trim() === '') {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'Required' });
    return;
  }

  // Must be exactly 7 characters
  if (val.length !== 7) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: '7 characters required' });
    return;
  }

  // Check for valid characters (0-7 only):
  if (!/^[0-7]+$/.test(val)) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'Only digits 0-7 allowed' });
    return;
  }

  // Check that each position contains either 0 or the correct day number
  for (let i = 0; i < 7; i++) {
    const dayNumber = i + 1; // 1 = Monday, 7 = Sunday
    const char = val[i];

    if (char !== '0' && parseInt(char) !== dayNumber) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Position ${i + 1} must be either 0 or ${dayNumber}`,
      });
      return;
    }
  }
});
