import { MenuItem, TextField } from '@mui/material';
import { ErrorLabel } from '@src/components/FormComponents';
import { type BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig, FormConfigInput, Maybe } from '@src/typesGlobal';
import React from 'react';
import { createLineFormUtils } from './createLineFormUtils';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

/**
 * Configuration for creating a line TextField component
 */
type LineTextFieldConfig<TLine, TMessage> = {
  formConfig: FormConfig<TLine>;
  useStore: () => BaseFormStore<TLine, TMessage>;
};

/**
 * Factory function to create a custom MUI TextField form input component
 * for line forms. Agnostic to the specific line and message types.
 * This component handles change events, key events, error display,
 * and focus management.
 * The Zustand store keeps track of the current focused input field,
 * and this component sets the focused state accordingly.
 */
export const createLineTextField = <TLine, TMessage>(config: LineTextFieldConfig<TLine, TMessage>) => {
  const { formConfig, useStore } = config;
  const utils = createLineFormUtils({ formConfig });

  type LineTextFieldProps = React.ComponentProps<typeof TextField> & {
    currentInputKey: keyof TLine;
  };

  const LineTextField: React.FC<LineTextFieldProps> = (props) => {
    const { currentInputKey, ...rest } = props;
    const thisInput: FormConfigInput<TLine> = utils.inputs[currentInputKey];

    const {
      formLine,
      formLineErrors: errors,
      setFormLinePartial: updateLine,
      formLineFocusKey,
      setFormLineFocusKey,
    } = useStore();

    // Check if this is a select field:
    const isSelect = thisInput.type === 'select';

    return (
      <TextField
        label={thisInput.label}
        name={thisInput.storeKey as string}
        select={isSelect}
        value={formLine?.[currentInputKey] || ''}
        onChange={utils.createOnChange(currentInputKey, updateLine)}
        onKeyDown={utils.createOnKeyHandler(utils.inputs, currentInputKey, updateLine, setFormLineFocusKey)}
        focused={formLineFocusKey === currentInputKey}
        helperText={showError(errors?.[currentInputKey as string])}
        error={!!errors?.[currentInputKey as string]}
        {...rest}
      >
        {isSelect &&
          thisInput.options?.map((option) => (
            <MenuItem key={option} value={option}>
              {option === '' ? '\u00A0' : option} {/* Non-breaking space for full height of option */}
            </MenuItem>
          ))}
      </TextField>
    );
  };

  return LineTextField;
};
