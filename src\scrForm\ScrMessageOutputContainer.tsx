import { MessageOutput } from '@src/components/MessageOutput';
import { useScrStore } from '@src/scrForm/useScrStore';
import type { Maybe } from '@src/typesGlobal';
import { zodErrorMap } from '@src/utils/utils';
import { useMemo } from 'react';
import type { ScrLine, ScrMessage } from './scrTypes';
import { formatScrMessage } from './scrUtils';
import { fullScrMessageSchema } from './scrValidationFullMsg';

/** Validates the full message, including the lines and their order. */
const validateFullMessage = (message: Maybe<ScrMessage>, lines: ScrLine[]) => {
  if (!message || lines.length === 0) return null;
  const msg = { message, lines };
  const parsed = fullScrMessageSchema.safeParse(msg);
  if (!parsed.success) {
    const errorMap = zodErrorMap(parsed.error);
    return errorMap;
  }
  return null;
};

/** Container for the SCR message output, ie the component that fetches the data from the store. */
export const ScrMessageOutputContainer: React.FC = () => {
  const { message, lines } = useScrStore();

  const error = useMemo(() => validateFullMessage(message, lines), [lines, message]);
  const fullText = useMemo(() => message && formatScrMessage(message, lines), [lines, message]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(fullText || '');
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return <MessageOutput error={error} fullText={fullText} handleCopy={handleCopy} />;
};
