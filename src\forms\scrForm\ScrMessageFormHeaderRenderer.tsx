import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import { ErrorLabel } from '@src/components/FormComponents';
import type { Maybe } from '@src/typesGlobal';
import { useRef } from 'react';
import { scrMessageFormConfig as formConfig } from './scrMessageFormConfig';
import { getOnChange } from './scrMessageFormUtils';
import { useScrStore } from './useScrStore';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

export const ScrMessageFormHeaderRenderer: React.FC = () => {
  const { inputs } = formConfig;
  const {
    // setMessage,
    formMessage,
    setFormMessagePartial: setMsgPart,
    // clearFormMessage,
    formMessageErrors: errors,
    // setFormMessageErrors,
    // clearFormMessageErrors,
  } = useScrStore();

  const formRef = useRef<HTMLDivElement>(null);

  // const handleAddMessage = () => {
  //   const parsed = scrMessageSchema.safeParse(formMessage);
  //   if (!parsed.success) {
  //     const errorMap = zodErrorMap(parsed.error);
  //     setFormMessageErrors(errorMap);
  //     return;
  //   }
  //   clearFormMessageErrors();
  //   setMessage(parsed.data);
  // };

  return (
    <div ref={formRef}>
      <Table /* component={Paper} */ size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '20%' }} />
          <col style={{ width: '20%' }} />
          <col style={{ width: '15%' }} />
          <col style={{ width: '45%' }} />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell sx={{ textAlign: 'left' }}>{formConfig.inputs.season.label}</TableCell>
            <TableCell sx={{ textAlign: 'left' }}>{formConfig.inputs.date.label}</TableCell>
            <TableCell sx={{ textAlign: 'left' }}>{formConfig.inputs.airport.label}</TableCell>
            <TableCell sx={{ textAlign: 'left' }}>{formConfig.inputs.creator.label}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <TextField
                value={formMessage?.season || ''}
                name="message.season"
                onChange={getOnChange(inputs.season.storeKey, setMsgPart)}
                error={!!errors?.season}
                helperText={showError(errors?.season)}
              />
            </TableCell>
            <TableCell>
              <TextField
                value={formMessage?.date || ''}
                name="message.date"
                onChange={getOnChange(inputs.date.storeKey, setMsgPart)}
                error={!!errors?.date}
                helperText={showError(errors?.date)}
              />
            </TableCell>
            <TableCell>
              <TextField
                value={formMessage?.airport || ''}
                name="message.airport"
                onChange={getOnChange(inputs.airport.storeKey, setMsgPart)}
                error={!!errors?.airport}
                helperText={showError(errors?.airport)}
              />
            </TableCell>
            <TableCell>
              <TextField
                name="message.creator"
                onBlur={(e) => {
                  setMsgPart({ creator: e.target.value });
                }}
                error={!!errors?.creator}
                helperText={showError(errors?.creator)}
              />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
