import {
  aircraftRegSchema,
  dateSchema,
  flightNumberSchema,
  gcrServiceTypeSchema,
  lineActionSchema,
  operatorSchema,
  schema2to4charsNotBlank,
  schema4charsNotBlank,
  schema4charsOrBlank,
  timeSchema,
} from '@src/validation/validationSchemas';
import { z } from 'zod';
import {
  type GcrFltArrLineFields,
  type GcrFltDepLineFields,
  type GcrFltLine,
  type GcrFltMessage,
  type GcrFltSharedLineFields,
} from './gcrFltTypes';

const gcrFltSharedFields = {
  action: lineActionSchema.refine((val) => val !== undefined, { message: 'Required' }),
  // date required
  date: dateSchema,
  seats: z.number().int().positive().max(999, { message: '1-999' }),
  // aircraftType required
  aircraftType: schema2to4charsNotBlank,
  // aircraftRegistration optional
  aircraftRegistration: aircraftRegSchema.optional(),
};

const gcrFltArrFields = {
  // operator arrival
  operatorArr: operatorSchema,
  // flightNumber arrival
  flightNumberArr: flightNumberSchema,
  // origin required
  origin: schema4charsNotBlank,
  // previous optional
  previous: schema4charsOrBlank.optional(),
  // time arrival
  timeArr: timeSchema,
  // Service type arrival
  stArr: gcrServiceTypeSchema,
};

const gcrFltDepFields = {
  // operator departure
  operatorDep: operatorSchema,
  // flightNumber
  flightNumberDep: flightNumberSchema,
  // time departure
  timeDep: timeSchema,
  // ON is for over night
  on: z.number().int().nonnegative().lte(9, { message: '0-9 or blank' }).optional(),
  // next optional
  next: schema4charsOrBlank.optional(),
  // destination required
  destination: schema4charsNotBlank,
  // Service type departure
  stDep: gcrServiceTypeSchema,
};

export const gcrFltSharedSchema = z.object(gcrFltSharedFields) satisfies z.ZodType<GcrFltSharedLineFields>;
export const gcrFltArrSchema = z.object(gcrFltArrFields) satisfies z.ZodType<GcrFltArrLineFields>;
export const gcrFltDepSchema = z.object(gcrFltDepFields) satisfies z.ZodType<GcrFltDepLineFields>;

export const StructuralPrefix = 'STRUCTURAL_';

export const gcrFltLineSchema = z
  // Join the schemas
  .object({
    ...gcrFltArrSchema.shape,
    ...gcrFltDepSchema.shape,
    ...gcrFltSharedSchema.shape,
  })
  // Losen up to make sure superRefine will run:
  .partial()
  // Apply logic to check if arrival, departure or both are filled:
  .superRefine((data, ctx) => {
    // console.count('superRefine');
    // console.log(`superRefine`, { data, ctx });
    const hasArrival = Object.keys(gcrFltArrSchema.shape).some((key) => !!data[key as keyof typeof data]);
    // console.log(`hasArrival`, hasArrival);
    const hasDeparture = Object.keys(gcrFltDepSchema.shape).some((key) => !!data[key as keyof typeof data]);
    // console.log(`hasDeparture`, hasDeparture);

    if (!hasArrival && !hasDeparture) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Arrival or Departure must be filled',
        path: [`${StructuralPrefix}Arrival or Departure`],
      });
      return;
    }

    if (hasArrival) {
      const result = gcrFltArrSchema.safeParse(data);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'When one arrival field is filled, all are required',
          path: [`${StructuralPrefix}Arrival`],
        });
        result.error.issues.forEach((issue) => ctx.addIssue(issue));
      }
    }

    if (hasDeparture) {
      const result = gcrFltDepSchema.safeParse(data);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'When one departure field is filled, all are required',
          path: [`${StructuralPrefix}Departure`],
        });
        result.error.issues.forEach((issue) => ctx.addIssue(issue));
      }
    }

    // Check shared fields in either case
    const sharedResult = gcrFltSharedSchema.safeParse(data);
    if (!sharedResult.success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Shared fields are required, except aircraftRegistration',
        path: [`${StructuralPrefix}Shared`],
      });
      sharedResult.error.issues.forEach((issue) => ctx.addIssue(issue));
    }
  })
  .transform((data) => data as GcrFltLine);
// satisfies z.ZodType<ScrLineFormInput>;
// }) satisfies z.ZodType<ScrLine>;

// Message schema:
export const gcrFltMessageSchema = z.object({
  airport: schema4charsOrBlank,
  si: z.string().optional(),
  gi: z.string().optional(),
}) satisfies z.ZodType<GcrFltMessage>;
