import type { ScrArrLine, ScrDepLine, ScrLineFormInput, ScrMessage } from './scrTypes';

const dummyArr: ScrArrLine = {
  action: 'N',
  operatorArr: 'KLM',
  flightNumberArr: '1103',
  dateFrom: '01JUN',
  dateTo: '18AUG',
  doop: '1030560',
  seats: 189,
  aircraftType: 'HOP',
  origin: 'CPH',
  previous: 'AMS',
  timeArr: '1300',
  stArr: 'K',
  frequency: undefined,
  aircraftRegistration: 'OY-KAL',
};

const dummyDep: ScrDepLine = {
  action: 'N',
  operatorDep: 'AF',
  flightNumberDep: '1234',
  dateFrom: '01JUN',
  dateTo: '18AUG',
  doop: '0030067',
  seats: 189,
  aircraftType: 'HOP',
  timeDep: '1400',
  on: 0,
  next: 'FRA',
  destination: 'LHR',
  stDep: 'K',
  frequency: undefined,
  aircraftRegistration: 'OY-KAL',
};

export const mockLines: ScrLineFormInput[] = [
  // Arrival:
  { ...dummyArr },
  // Full of errors:
  {
    ...dummyArr,
    operatorArr: 'KL',
    flightNumberArr: '3',
    operatorDep: 'AF',
    flightNumberDep: '4',
    dateFrom: '01JUNE',
    dateTo: '8AU',
    doop: '632',
    origin: 'CP',
    previous: 'AMS',
    timeArr: '130',
    timeDep: '140',
    on: 9,
    next: 'RA',
    frequency: '4',
    destination: 'LH',
  },
  // Departure:
  { ...dummyDep },
  // Change:
  {
    ...dummyArr,
    action: 'C',
    operatorArr: 'AF',
    flightNumberArr: '1234',
    doop: '0034560',
    seats: 220,
    aircraftType: '320',
    frequency: '2',
  },
  // Request:
  { ...dummyArr, action: 'R' },
];

const dummyMessage: ScrMessage = {
  season: 'W26',
  date: '21MAY',
  airport: 'CPH',
  creator: '<EMAIL>',
  si: 'STUFF',
  gi: 'MORE',
};

export const mockMessages: ScrMessage[] = [
  { ...dummyMessage },
  // Full of errors:
  {
    ...dummyMessage,
    season: 'W2',
    date: '2MAY',
    airport: 'CP',
    creator: 'your.email@example',
    si: '',
    gi: '',
  },
  // No errors:
  {
    ...dummyMessage,
    season: 'S25',
    date: '26MAY',
    airport: 'CPH',
    creator: '<EMAIL>',
    si: 'STUFF',
    gi: 'MORE',
  },
];
