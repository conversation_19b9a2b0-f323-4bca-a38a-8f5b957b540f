import { MessagePreview } from '@src/components/MessagePreview';
import { useMemo } from 'react';
import { formatScrMsgFooter, formatScrMsgHeader } from './scrUtils';
import { useScrStore } from './useScrStore';

export const ScrMessagePreviewContainer: React.FC = () => {
  const { message, clearMessage } = useScrStore();
  const msgHeader = useMemo(() => message && formatScrMsgHeader(message), [message]);
  const msgFooter = useMemo(() => message && formatScrMsgFooter(message), [message]);

  return <MessagePreview clearMessage={clearMessage} msgHeader={msgHeader} msgFooter={msgFooter} />;
};
