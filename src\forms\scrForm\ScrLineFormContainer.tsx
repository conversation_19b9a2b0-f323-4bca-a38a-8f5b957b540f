import { zodErrorMap } from '@src/utils/utils';
import { stripEmptyStringsFromInput } from '@src/validation/utils';
import { useCallback } from 'react';
import { ScrLineFormTableRenderer } from './ScrLineFormTableRenderer';
import { scrLineSchema } from './scrValidation';
import { useScrStore } from './useScrStore';

// TODO: Much functionality has been moved out and almost makes this component obsolete.
/**
 * The 'ScrLineFormContainer' creates an add line handler and passes
 * it to the 'ScrLineFormTableRenderer' component.
 */
export const ScrLineFormContainer: React.FC = () => {
  const { addLine, updateLine, formLine, formLineIndex, clearFormLine, setFormLineErrors, clearFormLineErrors } =
    useScrStore();

  const isEdit = formLineIndex !== null;

  /**
   * 'handleAddLine' will validate the formLine and add it to the store.
   * Memoize it to prevent unnecessary re-renders.
   */
  const handleAddLine = useCallback(() => {
    const cleanedFormLine = stripEmptyStringsFromInput(formLine);
    const parsed = scrLineSchema.safeParse(cleanedFormLine);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log(`errorMap`, errorMap);
      setFormLineErrors(errorMap);
      return;
    }
    clearFormLineErrors();
    isEdit ? updateLine(parsed.data) : addLine(parsed.data);
    clearFormLine();
  }, [addLine, clearFormLine, clearFormLineErrors, formLine, isEdit, setFormLineErrors, updateLine]);

  return <ScrLineFormTableRenderer handleAddLine={handleAddLine} />;
};
