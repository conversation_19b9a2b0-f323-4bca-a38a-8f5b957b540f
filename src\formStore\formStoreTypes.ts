// Store types
export type BaseFormStore<TLine, TMessage> = {
  message: TMessage | null;
  setMessage: (message: TMessage) => void;
  clearMessage: () => void;

  formMessage: Partial<TMessage> | null;
  setFormMessagePartial: (updates: Partial<TMessage>) => void;
  clearFormMessage: () => void;

  formMessageErrors: Record<string, string> | null;
  setFormMessageErrors: (error: Record<string, string> | null) => void;
  clearFormMessageErrors: () => void;

  lines: TLine[];
  addLine: (line: TLine) => void;
  updateLine: (updates: Partial<TLine>) => void;
  deleteLine: (index: number) => void;
  moveLineByOne: (index: number, direction: 'up' | 'down') => void;
  // TODO: clearAllLines currently not used, remove or implement?
  clearAllLines: () => void;

  formLine: Partial<TLine> | null;
  formLineIndex: number | null;
  setFormLine: (index?: number | null) => void;
  setFormLinePartial: (updates: Partial<TLine>) => void;
  clearFormLine: () => void;
  formLineFocusKey: keyof TLine | null;
  setFormLineFocusKey: (key: keyof TLine | null) => void;

  formLineErrors: Record<string, string> | null;
  setFormLineErrors: (errors: Record<string, string> | null) => void;
  clearFormLineErrors: () => void;
};
