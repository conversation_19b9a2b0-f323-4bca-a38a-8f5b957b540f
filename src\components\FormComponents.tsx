import styled from '@emotion/styled';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import type { Maybe } from '@src/typesGlobal';

// === General items ===

type ErrorLabelProps = {
  error: Maybe<string>;
};

export const ErrorLabel = (props: ErrorLabelProps) => {
  const { error } = props;
  if (!error) return null;
  return (
    <Tooltip title={error}>
      <span
        style={{
          textOverflow: 'ellipsis',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          display: 'block',
        }}
      >
        {error}
      </span>
    </Tooltip>
  );
};

export const FormButton = (props: React.ComponentProps<typeof Button>) => (
  <Button variant="contained" color="primary" size="small" sx={{ mr: 1, mb: 0 }} {...props} />
);

export const MockButton = (props: React.ComponentProps<typeof Button>) => (
  <Button variant="contained" size="small" sx={{ mr: 1, mb: 0, bgcolor: '#8acd7b', color: '#706f6f' }} {...props} />
);

// === Table form items ===

export const TableFormInnerBox = styled(Box)`
  margin: 0.5rem 0;
  &.right {
    display: flex;
    justify-content: flex-end;
  }
`;
