import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { appPages, PAGE_QUERY_PARAM } from '@src/constants';
import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export const Home: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  /** Replace current page in the query parameters and navigate to the new page. */
  const handleNavigation = useCallback(
    (pageParam: string) => {
      const newSearchParams = new URLSearchParams(location.search);
      newSearchParams.set(PAGE_QUERY_PARAM, pageParam);
      const newSearch = newSearchParams.toString();
      navigate(`/?${newSearch}`);
    },
    [location.search, navigate],
  );

  return (
    <Paper elevation={3} sx={{ padding: '2rem', maxWidth: '25rem', mx: 'auto', mt: '4rem' }}>
      <Typography variant="h2" gutterBottom sx={{ color: 'primary.main' }}>
        Aviation Messages
      </Typography>
      <Typography variant="h5" sx={{ mt: '2rem' }}>
        Select a module to get started:
      </Typography>
      <Stack spacing={'.75rem'} sx={{ mt: '1.5rem' }}>
        {appPages
          .filter((page) => page.pageParam !== 'home')
          .map((page) => (
            <Button
              key={page.pageParam}
              onClick={() => handleNavigation(page.pageParam)}
              variant="contained"
              fullWidth
              endIcon={<ArrowForwardIcon />}
              sx={{ py: 1.5, textTransform: 'none', m: '1rem' }}
            >
              {page.name}
            </Button>
          ))}
      </Stack>
    </Paper>
  );
};
