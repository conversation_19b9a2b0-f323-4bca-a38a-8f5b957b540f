import { LineList } from '@src/components/LineList';
import { validateActionSequence } from '@src/validation/actionSequenceValidation';
import { useEffect, useMemo, useRef } from 'react';
import { formatGcrFltLine } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';
import { StructuralPrefix } from './gcrFltValidation';

export const GcrFltLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, formLineErrors, moveLineByOne, clearAllLines } = useGcrFltStore();
  const lineSeqErrors = useRef<Record<string, string>>({});

  // Check for C/R action sequence errors
  useEffect(() => {
    lineSeqErrors.current = validateActionSequence(lines) || {};
  }, [lines]);

  const lineErrors = formLineErrors || {};
  const errors = Object.keys(lineErrors).reduce<Record<string, string>>(
    (acc, curr) => {
      if (curr.startsWith(StructuralPrefix)) {
        acc[curr.replace(StructuralPrefix, '')] = lineErrors[curr];
      }
      return acc;
    },
    { ...lineSeqErrors.current },
  );

  const formattedLines = useMemo(() => {
    return lines.map(formatGcrFltLine);
  }, [lines]);

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={setFormLine}
      handleDelete={deleteLine}
      handleMoveUp={(index) => moveLineByOne(index, 'up')}
      handleMoveDown={(index) => moveLineByOne(index, 'down')}
      clearAllLines={clearAllLines}
      errors={errors}
      hasErrors={hasErrors}
    />
  );
};
