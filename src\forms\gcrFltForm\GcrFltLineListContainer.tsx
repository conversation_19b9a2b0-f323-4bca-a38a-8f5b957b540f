import { LineList } from '@src/components/LineList';
import { useMemo } from 'react';
import { formatGcrFltLine } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';

export const GcrFltLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, moveLineByOne, clearAllLines } = useGcrFltStore();
  // Format the lines for display
  const formattedLines = useMemo(() => {
    return lines.map(formatGcrFltLine);
  }, [lines]);

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={setFormLine}
      handleDelete={deleteLine}
      handleMoveUp={(index) => moveLineByOne(index, 'up')}
      handleMoveDown={(index) => moveLineByOne(index, 'down')}
      clearAllLines={clearAllLines}
      errors={{}}
      hasErrors={false}
    />
  );
};
