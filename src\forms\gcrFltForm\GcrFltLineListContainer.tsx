import { LineList } from '@src/components/LineList';
import { useMemo } from 'react';
import { formatGcrFltLine } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';
import { StructuralPrefix } from './gcrFltValidation';

export const GcrFltLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, formLineErrors, moveLineByOne } = useGcrFltStore();

  const lineErrors = formLineErrors || {};
  const errors = Object.keys(lineErrors).reduce<Record<string, string>>((acc, curr) => {
    if (curr.startsWith(StructuralPrefix)) {
      acc[curr.replace(StructuralPrefix, '')] = lineErrors[curr];
    }
    return acc;
  }, {});

  const formattedLines = useMemo(() => {
    return lines.map(formatGcrFltLine);
  }, [lines]);

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={setFormLine}
      handleDelete={deleteLine}
      handleMoveUp={(index) => moveLineByOne(index, 'up')}
      handleMoveDown={(index) => moveLineByOne(index, 'down')}
      errors={errors}
      hasErrors={hasErrors}
    />
  );
};
