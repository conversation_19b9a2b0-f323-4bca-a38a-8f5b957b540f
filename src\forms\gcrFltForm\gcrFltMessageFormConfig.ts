import type { FormConfig } from '@src/typesGlobal';
import type { GcrFltMessage } from './gcrFltTypes';

export const gcrFltMessageFormConfig: FormConfig<GcrFltMessage> = {
  inputs: {
    airport: {
      storeKey: 'airport',
      label: 'Airport',
      type: 'text',
      toUpperCase: true,
    },
    si: {
      storeKey: 'si',
      label: 'SI',
      type: 'text',
    },
    gi: {
      storeKey: 'gi',
      label: 'GI',
      type: 'text',
    },
  },
};
