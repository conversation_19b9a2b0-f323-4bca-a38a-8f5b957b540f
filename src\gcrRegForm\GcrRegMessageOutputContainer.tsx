import { MessageOutput } from '@src/components/MessageOutput';
import type { Maybe } from '@src/typesGlobal';
import { zodErrorMap } from '@src/utils/utils';
import { useMemo } from 'react';
import { formatGcrRegMessage } from './gcrRegUtils';
import { useGcrRegStore } from './gcrRegStore';
import type { GcrRegLine, GcrRegMessage } from './gcrRegTypes';
import { fullGcrFltMessageSchema } from './gcrRegValidationFullMsg';

/** Validates the full message, including the lines and their order. */
const validateFullMessage = (message: Maybe<GcrRegMessage>, lines: GcrRegLine[]) => {
  if (!message || lines.length === 0) return null;
  const msg = { message, lines };
  const parsed = fullGcrFltMessageSchema.safeParse(msg);
  if (!parsed.success) {
    const errorMap = zodErrorMap(parsed.error);
    return errorMap;
  }
  return null;
};

/** Container for the SCR message output, ie the component that fetches the data from the store. */
export const GcrRegMessageOutputContainer: React.FC = () => {
  const { message, lines } = useGcrRegStore();

  const error = useMemo(() => validateFullMessage(message, lines), [lines, message]);
  const fullText = useMemo(() => message && formatGcrRegMessage(message, lines), [lines, message]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(fullText || '');
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return <MessageOutput error={error} fullText={fullText} handleCopy={handleCopy} />;
};
