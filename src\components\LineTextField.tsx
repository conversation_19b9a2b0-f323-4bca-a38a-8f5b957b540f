import type { BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig } from '@src/typesGlobal';
import type { ScrLine, ScrMessage } from '@src/scrForm/scrTypes';
import type { GcrFltLine, GcrFltMessage } from '@src/gcrFltForm/gcrFltTypes';
import type { GcrRegLine, GcrRegMessage } from '@src/gcrRegForm/gcrRegTypes';
import { TextField } from '@mui/material';
import { createLineFormUtils } from '@src/utils/createLineFormUtils';

export type AllLines = ScrLine | GcrFltLine | GcrRegLine;
export type AllMessages = ScrMessage | GcrFltMessage | GcrRegMessage;

type LineTextFieldProps = {
  fieldKey: keyof AllLines;
  useStore: () => BaseFormStore<AllLines, AllMessages>; // Pass the hook
  formConfig: FormConfig<AllLines>;
  // ... other props like label, placeholder, etc.
};

export const LineTextField = ({ fieldKey, useStore, formConfig }: LineTextFieldProps) => {
  const { formLine, setFormLinePartial, setFormLineFocusKey } = useStore(); // Call the hook

  // Create the utilities
  const { createOnChange, createOnKeyHandler } = createLineFormUtils({ formConfig });

  // Use the same logic from your factory but directly here
  const onChange = createOnChange(fieldKey, setFormLinePartial);
  const onKeyDown = createOnKeyHandler(formConfig.inputs, fieldKey, setFormLinePartial, setFormLineFocusKey);

  return (
    <TextField
      value={(formLine?.[fieldKey] as string) || ''}
      onChange={onChange}
      onKeyDown={onKeyDown}
      // ... other props
    />
  );
};
