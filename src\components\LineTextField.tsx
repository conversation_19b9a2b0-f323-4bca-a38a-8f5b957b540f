import type { BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig } from '@src/typesGlobal';

interface LineTextFieldProps<TLine, TMessage> {
  fieldKey: keyof TLine;
  store: BaseFormStore<TLine, TMessage>;
  formConfig: FormConfig<TLine>;
  // ... other props like label, placeholder, etc.
}

export const LineTextField = <TLine, TMessage>({
  fieldKey,
  store,
  formConfig,
}: // ...otherProps
LineTextFieldProps<TLine, TMessage>) => {
  const { line, setFormLinePartial, focusKey, setFocusKey } = store;

  // Use the same logic from your factory but directly here
  const onChange = createOnChange(fieldKey, setFormLinePartial);
  const onKeyDown = createOnKeyHandler(formConfig.inputs, fieldKey, setFormLinePartial, setFocusKey);

  return (
    <TextField
      value={line[fieldKey] || ''}
      onChange={onChange}
      onKeyDown={onKeyDown}
      // ... other props
    />
  );
};
