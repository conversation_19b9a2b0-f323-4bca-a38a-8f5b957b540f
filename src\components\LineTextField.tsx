import type { BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig } from '@src/typesGlobal';
import type { ScrLine, ScrMessage } from '@src/scrForm/scrTypes';
import type { GcrFltLine, GcrFltMessage } from '@src/gcrFltForm/gcrFltTypes';
import type { GcrRegLine, GcrRegMessage } from '@src/gcrRegForm/gcrRegTypes';
import { TextField } from '@mui/material';
import { createLineFormUtils } from '@src/utils/createLineFormUtils';

/**
 * Work in Progress:
 *
 * Goal: Replace the factory pattern (createLineTextField) with a generic LineTextField component
 * that supports all line types (ScrLine, GcrFltLine, GcrRegLine) using explicit union types.
 *
 * Benefits over factory approach:
 * - More explicit and easier to understand for developers (including myself 😂)
 * - No abstraction layer hiding the store usage
 * - Standard React component pattern vs factory pattern
 * - Better IDE support and TypeScript inference
 * - Simpler testing - just test a component with props vs testing a factory
 *
 * Usage will be:
 * <LineTextField
 *   fieldKey="someField"
 *   useStore={useScrStore}  // Pass the specific hook for the form type
 *   formConfig={scrFormConfig}
 * />
 *
 * TODO: Fix the store usage to pass the hook function instead of store object
 * to ensure proper React re-rendering when store values change.
 */

export type AllLines = ScrLine | GcrFltLine | GcrRegLine;
export type AllMessages = ScrMessage | GcrFltMessage | GcrRegMessage;

type LineTextFieldProps = {
  fieldKey: keyof AllLines;
  useStore: () => BaseFormStore<AllLines, AllMessages>; // Pass the hook
  formConfig: FormConfig<AllLines>;
  // ... Id
};

export const LineTextField = ({ fieldKey, useStore, formConfig }: LineTextFieldProps) => {
  const { formLine, setFormLinePartial, setFormLineFocusKey } = useStore(); // Call the hook

  // Create the utilities
  const { createOnChange, createOnKeyHandler } = createLineFormUtils({ formConfig });

  // Use the same logic from your factory but directly here
  const onChange = createOnChange(fieldKey, setFormLinePartial);
  const onKeyDown = createOnKeyHandler(formConfig.inputs, fieldKey, setFormLinePartial, setFormLineFocusKey);

  return (
    <TextField
      value={(formLine?.[fieldKey] as string) || ''}
      onChange={onChange}
      onKeyDown={onKeyDown}
      // ... other props
    />
  );
};
