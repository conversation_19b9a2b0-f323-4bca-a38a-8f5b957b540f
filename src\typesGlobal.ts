export type Maybe<T> = T | null | undefined;

// Form types
export type BaseFieldSet<T = unknown> = Record<string, T>;

export type FormConfigInput<TInputType> = {
  storeKey: keyof TInputType;
  label: string;
  type: 'text' | 'number' | 'select';
  options?: readonly string[];
  toUpperCase?: boolean;
  tabOrder?: number;
};

export type FormConfig<TInputType> = {
  inputs: {
    [K in keyof TInputType]-?: FormConfigInput<TInputType>;
  };
};

export type FormInputEvent = React.ChangeEvent<HTMLInputElement | HTMLSelectElement>;
