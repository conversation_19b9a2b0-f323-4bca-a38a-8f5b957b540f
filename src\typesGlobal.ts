import type { GcrFltLine, GcrFltMessage } from './forms/gcrFltForm/gcrFltTypes';
import type { GcrRegLine, GcrRegMessage } from './forms/gcrRegForm/gcrRegTypes';
import type { ScrLine, ScrMessage } from './forms/scrForm/scrTypes';

export type Maybe<T> = T | null | undefined;

// Form types
export type BaseFieldSet<T = unknown> = Record<string, T>;

export type FormConfigInput<TInputType> = {
  storeKey: keyof TInputType;
  label: string;
  type: 'text' | 'number' | 'select';
  options?: readonly string[];
  toUpperCase?: boolean;
  tabOrder?: number;
};

export type FormConfig<TInputType> = {
  inputs: {
    [K in keyof TInputType]-?: FormConfigInput<TInputType>;
  };
};

export type FormInputEvent = React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>;

export type FormLine = ScrLine | GcrRegLine | GcrFltLine;
export type FormMessage = ScrMessage | GcrRegMessage | GcrFltMessage;
