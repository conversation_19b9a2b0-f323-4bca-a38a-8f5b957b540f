import { createFormStore, type FormStore } from '@src/formStore/createFormStore';
import type { GcrFltLine, GcrFltLineFormInput, GcrFltMessage } from './gcrFltTypes';

const emptyLine: GcrFltLineFormInput = { action: 'N' };
const emptyMessage: Partial<GcrFltMessage> = {};

export const useGcrFltStore: FormStore<GcrFltLine, GcrFltMessage> = createFormStore<GcrFltLine, GcrFltMessage>({
  emptyLine,
  emptyMessage,
});
