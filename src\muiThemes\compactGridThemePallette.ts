import type { PaletteOptions } from '@mui/material/styles';

export const compactGridThemePallette: PaletteOptions = {
  primary: {
    main: '#277cb4', // Blue for headers and primary elements
    // ligther: '#58a7db',
    light: '#cbe3f2',
    dark: '#1b577e',
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#cbe3f2', // Light grey for backgrounds
    light: '#58a7db',
    dark: '#277cb4',
    contrastText: '#277cb4',
  },
  warning: {
    main: '#ed6c02', // Orange for warnings
    light: '#ff9800',
    dark: '#c55502',
    contrastText: '#ffffff',
  },
  error: {
    main: '#d32f2f', // Red for errors
    light: '#ef5350',
    dark: '#b71c1c',
    contrastText: '#ffffff',
  },
  success: {
    main: '#2e7d32', // Green for success
    light: '#4caf50',
    dark: '#1b5e20',
    contrastText: '#ffffff',
  },
  background: {
    default: '#f8f9fa', // Very light grey background
    paper: '#ffffff',
  },
  grey: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
  },
};
