import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import checker from 'vite-plugin-checker';
import path from 'path';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Checker config below enables type checking
    // and linting during development and build
    // and shows the errors in the browser overlay
    checker({
      typescript: {
        tsconfigPath: './tsconfig.app.json',
        buildMode: true,
        root: '.',
      },
      overlay: {
        initialIsOpen: true,
      },
      terminal: true,
    }),
  ],
  resolve: {
    alias: {
      '@src': path.resolve(__dirname, 'src'),
    },
  },
});
