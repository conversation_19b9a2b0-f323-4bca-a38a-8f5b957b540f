import { MessageOutput } from '@src/components/MessageOutput';
import type { Maybe } from '@src/typesGlobal';
import { zodErrorMap } from '@src/utils/utils';
import { useMemo } from 'react';
import type { GcrFltLine, GcrFltMessage } from './gcrFltTypes';
import { formatGcrFltMessage } from './gcrFltUtils';
import { fullGcrFltMessageSchema } from './gcrFltValidationFullMsg';
import { useGcrFltStore } from './useGcrFltStore';

/** Validates the full message, including the lines and their order. */
const validateFullMessage = (message: Maybe<GcrFltMessage>, lines: GcrFltLine[]) => {
  if (!message || lines.length === 0) return null;
  const msg = { message, lines };
  const parsed = fullGcrFltMessageSchema.safeParse(msg);
  if (!parsed.success) {
    const errorMap = zodErrorMap(parsed.error);
    return errorMap;
  }
  return null;
};

/** Container for the SCR message output, ie the component that fetches the data from the store. */
export const GcrFltMessageOutputContainer: React.FC = () => {
  const { message, lines } = useGcrFltStore();

  const error = useMemo(() => validateFullMessage(message, lines), [lines, message]);
  const fullText = useMemo(() => message && formatGcrFltMessage(message, lines), [lines, message]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(fullText || '');
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return <MessageOutput error={error} fullText={fullText} handleCopy={handleCopy} />;
};
