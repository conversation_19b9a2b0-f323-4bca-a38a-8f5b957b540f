/**
 * Example of how to create a new form store using the createFormStore factory
 *
 * This file demonstrates the pattern for creating new stores similar to
 * useScrStore and useGcrFltStore.
 */

import { createFormStore, createEmptyMessageWithDate } from './createFormStore';

// Example types for a hypothetical "Flight Request" form
export type FlightRequestLine = {
  action: 'N' | 'C' | 'R';
  flightNumber: string;
  origin: string;
  destination: string;
  date: string;
  time: string;
  aircraftType: string;
  seats: number;
};

export type FlightRequestMessage = {
  season: string;
  date: string;
  airport: string;
  creator?: string;
  requestType?: string;
  notes?: string;
};

// Create the store using the factory
const emptyLine: Partial<FlightRequestLine> = { action: 'N' };
const emptyMessage: Partial<FlightRequestMessage> = createEmptyMessageWithDate();

export const useFlightRequestStore = createFormStore<FlightRequestLine, FlightRequestMessage>({
  emptyLine,
  emptyMessage,
});

/**
 * Usage in a component:
 *
 * import { useFlightRequestStore } from '@src/utils/createFormStore.example';
 *
 * function MyComponent() {
 *   const {
 *     lines,
 *     addLine,
 *     formLine,
 *     setFormLinePartial,
 *     message,
 *     setMessage,
 *     // ... all other BaseFormStore methods
 *   } = useFlightRequestStore();
 *
 *   // Use the store methods as needed
 * }
 */
