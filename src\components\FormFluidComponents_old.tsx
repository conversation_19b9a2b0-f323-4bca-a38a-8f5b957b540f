import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import type { PropsWithChildren } from 'react';

// === Fluid form, first layout ===
export const FormSection: React.FC<PropsWithChildren<{ title: React.ReactNode }>> = (props) => {
  const { title, children } = props;
  return (
    <Card sx={{ mb: 1 }}>
      <CardContent sx={{ p: 1 }}>
        <Typography variant="subtitle1">{title}</Typography>
        <Grid container spacing={2} sx={{ p: 0 }}>
          {children}
        </Grid>
      </CardContent>
    </Card>
  );
};

const GridWidth = { xs: '100%', sm: '47%', md: '30%', lg: '30%', xl: '30%' };

export const FormGrid: React.FC<PropsWithChildren> = (props) => {
  const { children } = props;
  return <Grid sx={{ width: GridWidth }}>{children}</Grid>;
};

export const FormTextField: React.FC<React.ComponentProps<typeof TextField>> = (props) => {
  return <TextField fullWidth size="small" {...props} />;
};
