import { z } from 'zod';
import { gcrFltLineSchema, gcrFltMessageSchema } from './gcrFltValidation';

export const fullGcrFltMessageSchema = z
  .object({
    message: gcrFltMessageSchema, // header/footer
    lines: z.array(gcrFltLineSchema), // validated lines
  })
  .superRefine((data, ctx) => {
    const { lines } = data;

    // Find positions of all C and R lines
    const cIndexes = lines.map((l, i) => (l.action === 'C' ? i : -1)).filter((i) => i !== -1);
    const rIndexes = lines.map((l, i) => (l.action === 'R' ? i : -1)).filter((i) => i !== -1);

    // Rule 1: C (one or more) must always be followed by at least one R
    if (cIndexes.length > 0) {
      const earliestR = rIndexes.find((i) => i > cIndexes[0]);

      if (earliestR === undefined) {
        ctx.addIssue({
          path: ['lines'],
          code: z.ZodIssueCode.custom,
          message: 'At least one line with action R must come after one or more lines with action C.',
        });
      }
    }

    // Rule 2: R must always be preceded by at least one C
    if (rIndexes.length > 0) {
      const firstR = rIndexes[0];
      const hasPrecedingC = cIndexes.some((cIndex) => cIndex < firstR);

      if (!hasPrecedingC) {
        ctx.addIssue({
          path: ['lines'],
          code: z.ZodIssueCode.custom,
          message: 'Lines with action R must be preceded by at least one line with action C.',
        });
      }
    }
  });
