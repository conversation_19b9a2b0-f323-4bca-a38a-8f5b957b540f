import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { LineList } from '@src/components/LineList';
import { PageSectionWrap } from '@src/components/PageLayout';
import { GcrFltLineFormContainer } from './GcrFltLineFormContainer';
import { GcrFltMessageFormFooterRenderer } from './GcrFltMessageFormFooterRenderer';
import { GcrFltMessageFormHeaderRenderer } from './GcrFltMessageFormHeaderRenderer';
import { GcrFltMessageOutputContainer } from './GcrFltMessageOutputContainer';
import { formatGcrFltLine } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';

export const GcrFltPage: React.FC = () => {
  return (
    <>
      <Box sx={{ height: '1.5rem' }} />
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap>
          <GcrFltMessageFormHeaderRenderer />
        </PageSectionWrap>
      </Box>
      <Grid container spacing={1} sx={{ alignItems: 'flex-start' }}>
        <Grid sx={{ mr: '1rem', width: '40%', minWidth: '80rem', maxWidth: '90rem' }}>
          <PageSectionWrap>
            <GcrFltLineFormContainer />
          </PageSectionWrap>
        </Grid>
        <Grid sx={{ minWidth: '50rem', maxWidth: '50rem' }}>
          <PageSectionWrap>
            <LineList useStore={useGcrFltStore} formatLine={formatGcrFltLine} />
          </PageSectionWrap>
        </Grid>
      </Grid>
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap>
          <GcrFltMessageFormFooterRenderer />
        </PageSectionWrap>
        <PageSectionWrap>
          <GcrFltMessageOutputContainer />
        </PageSectionWrap>
      </Box>
    </>
  );
};
