import type { FormInputEvent } from '@src/typesGlobal';
import type { ScrMessage } from './scrTypes';
import { scrMessageFormConfig } from './scrMessageFormConfig';

/**
 * 'getOnChange' will given a field key, and a store method to update the message,
 * return a function that takes an event
 * and updates the store for that field.
 * Memoize it to prevent unnecessary re-renders.
 */
export const getOnChange =
  (key: keyof ScrMessage, setFormMsg: (updates: Partial<ScrMessage>) => void) => (e: FormInputEvent) => {
    let value: ScrMessage[keyof ScrMessage] = e.target.value;
    // Check if this field should be uppercased
    if (scrMessageFormConfig.inputs[key]?.toUpperCase) {
      value = e.target.value?.toUpperCase();
    }
    // Check if this field should be a number type
    // if (scrMessageFormConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
    //   value = Number(e.target.value);
    // }
    setFormMsg({ [key]: value });
  };
