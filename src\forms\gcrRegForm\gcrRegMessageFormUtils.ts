import type { FormInputEvent } from '@src/typesGlobal';
import type { GcrRegMessage } from './gcrRegTypes';
import { gcrRegMessageFormConfig } from './gcrRegMessageFormConfig';

/**
 * 'getOnChange' will given a field key, and a store method to update the message,
 * return a function that takes an event
 * and updates the store for that field.
 * Memoize it to prevent unnecessary re-renders.
 */
export const getOnChange =
  (key: keyof GcrRegMessage, setFormMsg: (updates: Partial<GcrRegMessage>) => void) => (e: FormInputEvent) => {
    let value: GcrRegMessage[keyof GcrRegMessage] = e.target.value;
    // Check if this field should be uppercased
    if (gcrRegMessageFormConfig.inputs[key]?.toUpperCase) {
      value = e.target.value?.toUpperCase();
    }
    // Check if this field should be a number type
    // if (gcrRegMessageFormConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
    //   value = Number(e.target.value);
    // }
    setFormMsg({ [key]: value });
  };
