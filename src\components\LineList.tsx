import styled from '@emotion/styled';
import { ArrowDownward, ArrowUpward, Delete, Edit } from '@mui/icons-material';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import type { FormStore } from '@src/formStore/createFormStore';
import { type LineWithAction } from '@src/validation/actionSequenceValidation';
import { useCallback, useMemo } from 'react';

const col1style = {
  maxWidth: 0, // forces it to only take available space
  overflow: 'hidden',
};

const col2style = { width: '100px', whiteSpace: 'nowrap' };

type LineListProps<TLine extends LineWithAction, TMessage> = {
  useStore: FormStore<TLine, TMessage>;
  formatLine: (line: TLine) => string;
};

export const LineList = <TLine extends LineWithAction, TMessage>(props: LineListProps<TLine, TMessage>) => {
  const { useStore, formatLine } = props;

  const { lines, deleteLine, setFormLine, moveLineByOne, clearAllLines } = useStore();

  // Format the lines for display
  const formattedLines = useMemo(() => {
    return lines.map(formatLine);
  }, [lines, formatLine]);

  // Handlers to clear all lines:
  const clearAll = useCallback(() => {
    if (confirm('Are you sure you want to clear all lines?')) clearAllLines();
  }, [clearAllLines]);

  return (
    <Box>
      <TableContainer sx={{ maxHeight: '15rem', overflowY: 'auto', mb: 2, mx: 'auto' }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell sx={col1style}>Lines Added</TableCell>
              <TableCell align="right" sx={col2style}>
                Actions
                <IconButton size="small" onClick={clearAll} title="Clear all lines">
                  <Delete fontSize="small" /* sx={{ color: 'common.white' }} */ />
                </IconButton>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {formattedLines.map((line, index) => (
              <TableRow key={index} hover>
                <TableCell sx={col1style}>
                  <StyledPre>{line}</StyledPre>
                </TableCell>
                <TableCell align="right" sx={col2style}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <IconButton size="small" onClick={() => moveLineByOne(index, 'up')} disabled={index === 0}>
                      <ArrowUpward fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => moveLineByOne(index, 'down')}
                      disabled={index === formattedLines.length - 1}
                    >
                      <ArrowDownward fontSize="small" />
                    </IconButton>
                    <IconButton size="small" onClick={() => setFormLine(index)}>
                      <Edit fontSize="small" />
                    </IconButton>
                    <IconButton size="small" onClick={() => deleteLine(index)}>
                      <Delete fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
            {formattedLines.length === 0 && (
              <TableRow>
                <TableCell colSpan={2} align="center">
                  No lines added
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

const StyledPre = styled.pre`
  /* background-color: lime; */
  margin: 0;
  font-family: monospace;
  overflow-x: auto;
  /* Multi line: */
  white-space: pre-wrap;
  /* Single line: */
  /* white-space: pre; */

  /* Scrollbar styling only relevant if forcing
     single line */

  /* Firefox (works fine without this): */
  /* scrollbar-width: thin; */
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
`;
