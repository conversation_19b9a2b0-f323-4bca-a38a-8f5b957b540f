import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import { ErrorLabel } from '@src/components/FormComponents';
import type { Maybe } from '@src/typesGlobal';
import { useRef } from 'react';
import { gcrRegMessageFormConfig as formConfig } from './gcrRegMessageFormConfig';
import { getOnChange } from './gcrRegMessageFormUtils';
import { useGcrRegStore } from './gcrRegStore';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

export const GcrRegMessageFormHeaderRenderer: React.FC = () => {
  const { inputs } = formConfig;
  const {
    formMessage,
    setFormMessagePartial: setMsgPart,
    formMessageErrors: errors,
  } = useGcrRegStore();

  const formRef = useRef<HTMLDivElement>(null);

  return (
    <div ref={formRef}>
      <Table /* component={Paper} */ size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '25%' }} />
          <col style={{ width: '25%' }} />
          <col style={{ width: '25%' }} />
          <col style={{ width: '25%' }} />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell>{formConfig.inputs.airport.label}</TableCell>
            <TableCell></TableCell>
            <TableCell></TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <TextField
                value={formMessage?.airport || ''}
                name="message.airport"
                onChange={getOnChange(inputs.airport.storeKey, setMsgPart)}
                error={!!errors?.airport}
                helperText={showError(errors?.airport)}
              />
            </TableCell>
            <TableCell colSpan={3}></TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
