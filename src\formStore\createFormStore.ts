import type { Draft } from 'immer';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { BaseFormStore } from './formStoreTypes';

/**
 * Configuration for creating a form store
 */
interface FormStoreConfig<TLine, TMessage> {
  /** Empty line object for form initialization */
  emptyLine: Partial<TLine>;
  /** Empty message object for form initialization */
  emptyMessage: Partial<TMessage>;
}

/**
 * Factory function to create a Zustand store with immer middleware
 * that implements the BaseFormStore interface.
 *
 * @param config Configuration object containing empty line and message objects
 * @returns A Zustand store hook
 */
export const createFormStore = <TLine, TMessage>(config: FormStoreConfig<TLine, TMessage>) => {
  const { emptyLine, emptyMessage } = config;
  // console.log(`createFormStore`);

  type StoreState = BaseFormStore<TLine, TMessage>;

  return create<StoreState>()(
    immer((set) => ({
      // Message:
      message: null,
      setMessage: (message) =>
        set((state) => {
          state.message = message as Draft<TMessage>;
        }),
      clearMessage: () =>
        set((state) => {
          state.message = null;
        }),

      // Message form:
      formMessage: emptyMessage,
      setFormMessagePartial: (updates) =>
        set((state) => {
          Object.assign(state.formMessage || {}, updates);
        }),
      clearFormMessage: () =>
        set((state) => {
          state.formMessage = emptyMessage as Draft<Partial<TMessage>>;
        }),

      // Form message error map:
      formMessageErrors: null,
      setFormMessageErrors: (errors) =>
        set((state) => {
          state.formMessageErrors = errors;
        }),
      clearFormMessageErrors: () =>
        set((state) => {
          state.formMessageErrors = null;
        }),

      // Lines:
      lines: [],
      addLine: (line) =>
        set((state) => {
          state.lines.push(line as Draft<TLine>);
        }),
      updateLine: (updates) =>
        set((state) => {
          if (state.formLineIndex === null) return;
          const line = state.lines[state.formLineIndex];
          Object.keys(updates).forEach((key) => {
            (line as Record<string, unknown>)[key] = (updates as Record<string, unknown>)[key];
          });
        }),
      deleteLine: (index) =>
        set((state) => {
          state.lines.splice(index, 1);
        }),
      moveLineByOne: (index, direction) =>
        set((state) => {
          const newIndex = direction === 'up' ? index - 1 : index + 1;
          if (newIndex < 0 || newIndex >= state.lines.length) return;
          const [line] = state.lines.splice(index, 1);
          state.lines.splice(newIndex, 0, line);
        }),
      clearAllLines: () =>
        set((state) => {
          state.lines = [];
        }),

      // Edit line:
      formLine: emptyLine,
      formLineIndex: null,
      setFormLine: (index = null) =>
        set((state) => {
          state.formLineIndex = index;
          if (index === null) return;
          state.formLine = state.lines[index || 0];
        }),
      setFormLinePartial: (updates) =>
        set((state) => {
          Object.assign(state.formLine || {}, updates);
        }),
      clearFormLine: () =>
        set((state) => {
          state.formLine = emptyLine as Draft<Partial<TLine>>;
          state.formLineIndex = null;
          state.formLineErrors = null;
          state.formLineFocusKey = null;
        }),
      formLineFocusKey: null,
      setFormLineFocusKey: (key) =>
        set((state) => {
          state.formLineFocusKey = key as Draft<keyof TLine>;
        }),

      // Line error state:
      formLineErrors: null,
      setFormLineErrors: (errors) =>
        set((state) => {
          state.formLineErrors = errors;
        }),
      clearFormLineErrors: () =>
        set((state) => {
          state.formLineErrors = null;
        }),
    })),
  );
};

/**
 * Helper function to create the standard empty message with today's date
 * in the format used by the application (DDMMM)
 */
export const createEmptyMessageWithDate = (): { date: string } => {
  const today = new Date();
  const month = today.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
  const day = today.getDate().toString().padStart(2, '0');
  const todayFormatted = `${day}${month}`;
  return { date: todayFormatted };
};
