import Grid from '@mui/material/Grid';
import { LineList } from '@src/components/LineList';
import { PageSectionWrap } from '@src/components/PageLayout';
import { GcrRegLineFormContainer } from './GcrRegLineFormContainer';
import { GcrRegMessageFormFooterRenderer } from './GcrRegMessageFormFooterRenderer';
import { GcrRegMessageFormHeaderRenderer } from './GcrRegMessageFormHeaderRenderer';
import { GcrRegMessageOutputContainer } from './GcrRegMessageOutputContainer';
import { useGcrRegStore } from './gcrRegStore';
import { formatGcrRegLine } from './gcrRegUtils';

export const GcrRegPage: React.FC = () => {
  return (
    <>
      <PageSectionWrap>
        <GcrRegMessageFormHeaderRenderer />
      </PageSectionWrap>
      <PageSectionWrap>
        <GcrRegLineFormContainer />
      </PageSectionWrap>
      <PageSectionWrap>
        <LineList useStore={useGcrRegStore} formatLine={formatGcrRegLine} />
      </PageSectionWrap>
      <PageSectionWrap>
        <Grid container spacing={1} sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }}>
          <Grid size={5.8}>
            <GcrRegMessageFormFooterRenderer />
          </Grid>
          <Grid size={5.8}>
            <GcrRegMessageOutputContainer />
          </Grid>
        </Grid>
      </PageSectionWrap>
    </>
  );
};
