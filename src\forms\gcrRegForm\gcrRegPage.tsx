import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { PageSectionWrap } from '@src/components/PageLayout';
import { GcrRegLineFormContainer } from './GcrRegLineFormContainer';
import { GcrRegLineListContainer } from './GcrRegLineListContainer';
import { GcrRegMessageFormFooterRenderer } from './GcrRegMessageFormFooterRenderer';
import { GcrRegMessageFormHeaderRenderer } from './GcrRegMessageFormHeaderRenderer';
import { GcrRegMessageOutputContainer } from './GcrRegMessageOutputContainer';

export const GcrRegPage: React.FC = () => {
  return (
    <>
      <Box sx={{ height: '1.5rem' }} />
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap>
          <GcrRegMessageFormHeaderRenderer />
        </PageSectionWrap>
      </Box>
      <Grid container spacing={1} sx={{ alignItems: 'flex-start' }}>
        <Grid sx={{ mr: '1rem', width: '40%', minWidth: '80rem', maxWidth: '90rem' }}>
          <PageSectionWrap>
            <GcrRegLineFormContainer />
          </PageSectionWrap>
        </Grid>
        <Grid sx={{ minWidth: '50rem', maxWidth: '50rem' }}>
          <PageSectionWrap>
            <GcrRegLineListContainer />
          </PageSectionWrap>
        </Grid>
      </Grid>
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap>
          <GcrRegMessageFormFooterRenderer />
        </PageSectionWrap>
        <PageSectionWrap>
          <GcrRegMessageOutputContainer />
        </PageSectionWrap>
      </Box>
    </>
  );
};
