import { GenericFormPage } from '@src/components/GenericFormPage';
import { LineList } from '@src/components/LineList';
import { GcrRegLineFormContainer } from './GcrRegLineFormContainer';
import { GcrRegMessageFormFooterRenderer } from './GcrRegMessageFormFooterRenderer';
import { GcrRegMessageFormHeaderRenderer } from './GcrRegMessageFormHeaderRenderer';
import { GcrRegMessageOutputContainer } from './GcrRegMessageOutputContainer';
import { useGcrRegStore } from './gcrRegStore';
import { formatGcrRegLine } from './gcrRegUtils';

export const GcrRegPage: React.FC = () => {
  return (
    <GenericFormPage
      // messageHeaderRenderer={GcrRegMessageFormHeaderRenderer}
      messageHeaderForm={<GcrRegMessageFormHeaderRenderer />}
      // lineFormContainer={GcrRegLineFormContainer}
      lineForm={<GcrRegLineFormContainer />}
      lineList={<LineList useStore={useGcrRegStore} formatLine={formatGcrRegLine} />}
      // messageFooterRenderer={GcrRegMessageFormFooterRenderer}
      messageFooterForm={<GcrRegMessageFormFooterRenderer />}
      // messageOutputContainer={GcrRegMessageOutputContainer}
      messageOutput={<GcrRegMessageOutputContainer />}
    />
  );
};
