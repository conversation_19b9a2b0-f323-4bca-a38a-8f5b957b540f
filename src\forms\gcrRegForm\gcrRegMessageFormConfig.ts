import type { FormConfig } from '@src/typesGlobal';
import type { GcrRegMessage } from './gcrRegTypes';

export const gcrRegMessageFormConfig: FormConfig<GcrRegMessage> = {
  inputs: {
    airport: {
      storeKey: 'airport',
      label: 'Airport',
      type: 'text',
      toUpperCase: true,
    },
    si: {
      storeKey: 'si',
      label: 'SI',
      type: 'text',
    },
    gi: {
      storeKey: 'gi',
      label: 'GI',
      type: 'text',
    },
  },
};
