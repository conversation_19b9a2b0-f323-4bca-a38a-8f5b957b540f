import type { GcrServiceType, LineAction } from '@src/validation/validationSchemas';

/** SCR line shared fields */
export type GcrFltSharedLineFields = {
  action: LineAction;
  date: string;
  seats: number;
  aircraftType: string;
  aircraftRegistration?: string;
};
/** SCR line arrival fields */
export type GcrFltArrLineFields = {
  operatorArr: string;
  flightNumberArr: string;
  origin: string;
  previous?: string;
  timeArr: string;
  stArr: GcrServiceType;
};
/** SCR line departure fields */
export type GcrFltDepLineFields = {
  operatorDep: string;
  flightNumberDep: string;
  timeDep: string;
  on?: number;
  next?: string;
  destination: string;
  stDep: GcrServiceType;
};

/** SCR arrival line type */
export type GcrFltArrLine = GcrFltArrLineFields & GcrFltSharedLineFields;
/** SCR departure line type */
export type GcrFltDepLine = GcrFltDepLineFields & GcrFltSharedLineFields;

/** SCR line form validation */
export type GcrFltLine = GcrFltSharedLineFields & GcrFltArrLineFields & GcrFltDepLineFields;
/** SCR line form input */
export type GcrFltLineFormInput = Partial<GcrFltLine>;

/** For message form validation */
export type GcrFltMessage = {
  /** Coordinated airport IATA code (e.g. CPH) */
  airport: string;
  /* Scheduled information */
  si?: string;
  /** General information */
  gi?: string;
};

/** For full message validation */
export type GcrFltFullMessage = {
  message: GcrFltMessage;
  lines: GcrFltLine[];
};
