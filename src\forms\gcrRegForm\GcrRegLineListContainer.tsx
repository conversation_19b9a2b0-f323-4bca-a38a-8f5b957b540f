import { LineList } from '@src/components/LineList';
import { useMemo } from 'react';
import { useGcrRegStore } from './gcrRegStore';
import { formatGcrRegLine } from './gcrRegUtils';
import { StructuralPrefix } from './gcrRegValidation';

export const GcrRegLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, formLineErrors, moveLineByOne, clearAllLines } = useGcrRegStore();

  const lineErrors = formLineErrors || {};
  const errors = Object.keys(lineErrors).reduce<Record<string, string>>((acc, curr) => {
    if (curr.startsWith(StructuralPrefix)) {
      acc[curr.replace(StructuralPrefix, '')] = lineErrors[curr];
    }
    return acc;
  }, {});

  const formattedLines = useMemo(() => {
    return lines.map(formatGcrRegLine);
  }, [lines]);

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={setFormLine}
      handleDelete={deleteLine}
      handleMoveUp={(index) => moveLineByOne(index, 'up')}
      handleMoveDown={(index) => moveLineByOne(index, 'down')}
      clearAllLines={clearAllLines}
      errors={errors}
      hasErrors={hasErrors}
    />
  );
};
