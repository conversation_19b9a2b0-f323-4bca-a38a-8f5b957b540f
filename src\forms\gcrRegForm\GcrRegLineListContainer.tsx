import { LineList } from '@src/components/LineList';
import { useMemo } from 'react';
import { useGcrRegStore } from './gcrRegStore';
import { formatGcrRegLine } from './gcrRegUtils';

export const GcrRegLineListContainer: React.FC = () => {
  const { lines, deleteLine, setFormLine, moveLineByOne, clearAllLines } = useGcrRegStore();

  // Format the lines for display
  const formattedLines = useMemo(() => {
    return lines.map(formatGcrRegLine);
  }, [lines]);

  return (
    <LineList
      formattedLines={formattedLines}
      handleEdit={setFormLine}
      handleDelete={deleteLine}
      handleMoveUp={(index) => moveLineByOne(index, 'up')}
      handleMoveDown={(index) => moveLineByOne(index, 'down')}
      clearAllLines={clearAllLines}
      errors={{}}
      hasErrors={false}
    />
  );
};
