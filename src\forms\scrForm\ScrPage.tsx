import { GenericFormPage } from '@src/components/GenericFormPage';
import { LineList } from '@src/components/LineList';
import { ScrLineFormContainer } from './ScrLineFormContainer';
import { ScrMessageFormFooterRenderer } from './ScrMessageFormFooterRenderer';
import { ScrMessageFormHeaderRenderer } from './ScrMessageFormHeaderRenderer';
import { ScrMessageOutputContainer } from './ScrMessageOutputContainer';
import { formatScrLine } from './scrUtils';
import { useScrStore } from './useScrStore';

export const ScrPage: React.FC = () => {
  return (
    <GenericFormPage
      messageHeaderForm={<ScrMessageFormHeaderRenderer />}
      lineForm={<ScrLineFormContainer />}
      lineList={<LineList useStore={useScrStore} formatLine={formatScrLine} />}
      messageFooterForm={<ScrMessageFormFooterRenderer />}
      messageOutput={<ScrMessageOutputContainer />}
    />
  );
};
