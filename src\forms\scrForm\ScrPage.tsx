import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { LineList } from '@src/components/LineList';
import { PageSectionWrap } from '@src/components/PageLayout';
import { ScrLineFormContainer } from './ScrLineFormContainer';
import { ScrMessageFormFooterRenderer } from './ScrMessageFormFooterRenderer';
import { ScrMessageFormHeaderRenderer } from './ScrMessageFormHeaderRenderer';
import { ScrMessageOutputContainer } from './ScrMessageOutputContainer';
import { formatScrLine } from './scrUtils';
import { useScrStore } from './useScrStore';

export const ScrPage: React.FC = () => {
  return (
    <>
      <Box sx={{ height: '1.5rem' }} />
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap>
          <ScrMessageFormHeaderRenderer />
        </PageSectionWrap>
      </Box>
      <Grid container spacing={1} sx={{ alignItems: 'flex-start' }}>
        <Grid sx={{ mr: '1rem', width: '40%', minWidth: '80rem', maxWidth: '90rem' }}>
          <PageSectionWrap>
            <ScrLineFormContainer />
          </PageSectionWrap>
        </Grid>
        <Grid sx={{ minWidth: '50rem', maxWidth: '50rem' }}>
          <PageSectionWrap>
            <LineList useStore={useScrStore} formatLine={formatScrLine} />
          </PageSectionWrap>
        </Grid>
      </Grid>
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap /* sx={{ maxWidth: '50rem' }} */>
          <ScrMessageFormFooterRenderer />
        </PageSectionWrap>
        <PageSectionWrap>
          <ScrMessageOutputContainer />
        </PageSectionWrap>
      </Box>
    </>
  );
};
