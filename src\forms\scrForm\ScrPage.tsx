import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { PageSectionWrap } from '@src/components/PageLayout';
import { ScrLineFormContainer } from './ScrLineFormContainer';
import { ScrLineListContainer } from './ScrLineListContainer';
import { ScrMessageFormFooterRenderer } from './ScrMessageFormFooterRenderer';
import { ScrMessageFormHeaderRenderer } from './ScrMessageFormHeaderRenderer';
import { ScrMessageOutputContainer } from './ScrMessageOutputContainer';
import { ScrMessagePreviewContainer } from './ScrMessagePreviewContainer';

export const ScrPage: React.FC = () => {
  return (
    <>
      <Box sx={{ height: '1.5rem' }} />
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap /* sx={{ maxWidth: '50rem' }} */>
          <ScrMessageFormHeaderRenderer />
        </PageSectionWrap>
      </Box>
      <Grid container spacing={1} sx={{ alignItems: 'flex-start' }}>
        <Grid sx={{ mr: '1rem', width: '40%', minWidth: '110rem', maxWidth: '120rem' }}>
          <PageSectionWrap /* sx={{ maxWidth: '99%' }} */>
            <ScrLineFormContainer />
          </PageSectionWrap>
        </Grid>
        <Grid sx={{ minWidth: '30rem', maxWidth: '40rem', width: '100%' /* , mt: '-0.75rem' */ }}>
          <PageSectionWrap>
            <ScrLineListContainer />
          </PageSectionWrap>
        </Grid>
      </Grid>
      <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
        <PageSectionWrap /* sx={{ maxWidth: '50rem' }} */>
          <ScrMessageFormFooterRenderer />
        </PageSectionWrap>
        <PageSectionWrap>
          <ScrMessagePreviewContainer />
        </PageSectionWrap>
        <PageSectionWrap>
          <ScrMessageOutputContainer />
        </PageSectionWrap>
      </Box>
    </>
  );
};
