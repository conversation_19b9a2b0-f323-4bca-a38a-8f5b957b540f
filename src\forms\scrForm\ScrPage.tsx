import Grid from '@mui/material/Grid';
import { LineList } from '@src/components/LineList';
import { PageSectionWrap } from '@src/components/PageLayout';
import { ScrLineFormContainer } from './ScrLineFormContainer';
import { ScrMessageFormFooterRenderer } from './ScrMessageFormFooterRenderer';
import { ScrMessageFormHeaderRenderer } from './ScrMessageFormHeaderRenderer';
import { ScrMessageOutputContainer } from './ScrMessageOutputContainer';
import { formatScrLine } from './scrUtils';
import { useScrStore } from './useScrStore';

export const ScrPage: React.FC = () => {
  return (
    <>
      <PageSectionWrap>
        <ScrMessageFormHeaderRenderer />
      </PageSectionWrap>
      <PageSectionWrap>
        <ScrLineFormContainer />
      </PageSectionWrap>
      <PageSectionWrap>
        <LineList useStore={useScrStore} formatLine={formatScrLine} />
      </PageSectionWrap>
      <PageSectionWrap>
        <Grid container spacing={1} sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }}>
          <Grid size={5.8}>
            <ScrMessageFormFooterRenderer />
          </Grid>
          <Grid size={5.8}>
            <ScrMessageOutputContainer />
          </Grid>
        </Grid>
      </PageSectionWrap>
    </>
  );
};
