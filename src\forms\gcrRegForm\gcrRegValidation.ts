import {
  aircraftRegSchema,
  dateSchema,
  gcrServiceTypeSchema,
  lineActionSchema,
  schema2to4charsNotBlank,
  schema4charsNotBlank,
  schema4charsOrBlank,
  timeSchema,
} from '@src/validation/validationSchemas';
import { z } from 'zod';
import {
  type GcrRegArrLineFields,
  type GcrRegDepLineFields,
  type GcrRegLine,
  type GcrRegMessage,
  type GcrRegSharedLineFields,
} from './gcrRegTypes';

const gcrRegSharedFields = {
  action: lineActionSchema.refine((val) => val !== undefined, { message: 'Required' }),
  // aircraftRegistration required
  aircraftRegistration: aircraftRegSchema,
  // date required
  date: dateSchema,
  seats: z.number().int().positive().max(999, { message: '1-999' }),
  // aircraftType required
  aircraftType: schema2to4charsNotBlank,
};

const gcrRegArrFields = {
  // origin required
  origin: schema4charsNotBlank,
  // previous optional
  previous: schema4charsOrBlank.optional(),
  // time arrival
  timeArr: timeSchema,
  // Service type arrival
  stArr: gcrServiceTypeSchema,
};

const gcrRegDepFields = {
  // time departure
  timeDep: timeSchema,
  // ON is for over night
  on: z.number().int().nonnegative().lte(9, { message: '0-9 or blank' }).optional(),
  // next optional
  next: schema4charsOrBlank.optional(),
  // destination required
  destination: schema4charsNotBlank,
  // Service type departure
  stDep: gcrServiceTypeSchema,
};

export const gcrRegSharedSchema = z.object(gcrRegSharedFields) satisfies z.ZodType<GcrRegSharedLineFields>;
export const gcrRegArrSchema = z.object(gcrRegArrFields) satisfies z.ZodType<GcrRegArrLineFields>;
export const gcrRegDepSchema = z.object(gcrRegDepFields) satisfies z.ZodType<GcrRegDepLineFields>;

export const StructuralPrefix = 'STRUCTURAL_';

export const gcrRegLineSchema = z
  // Join the schemas
  .object({
    ...gcrRegArrSchema.shape,
    ...gcrRegDepSchema.shape,
    ...gcrRegSharedSchema.shape,
  })
  // Losen up to make sure superRefine will run:
  .partial()
  // Apply logic to check if arrival, departure or both are filled:
  .superRefine((data, ctx) => {
    // console.count('superRefine');
    // console.log(`superRefine`, { data, ctx });
    const hasArrival = Object.keys(gcrRegArrSchema.shape).some((key) => !!data[key as keyof typeof data]);
    // console.log(`hasArrival`, hasArrival);
    const hasDeparture = Object.keys(gcrRegDepSchema.shape).some((key) => !!data[key as keyof typeof data]);
    // console.log(`hasDeparture`, hasDeparture);

    if (!hasArrival && !hasDeparture) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Arrival or Departure must be filled',
        path: [`${StructuralPrefix}Arrival or Departure`],
      });
      return;
    }

    if (hasArrival) {
      const result = gcrRegArrSchema.safeParse(data);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'When one arrival field is filled, all are required',
          path: [`${StructuralPrefix}Arrival`],
        });
        result.error.issues.forEach((issue) => ctx.addIssue(issue));
      }
    }

    if (hasDeparture) {
      const result = gcrRegDepSchema.safeParse(data);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'When one departure field is filled, all are required',
          path: [`${StructuralPrefix}Departure`],
        });
        result.error.issues.forEach((issue) => ctx.addIssue(issue));
      }
    }

    // Check shared fields in either case
    const sharedResult = gcrRegSharedSchema.safeParse(data);
    if (!sharedResult.success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Shared fields are required, except aircraftRegistration',
        path: [`${StructuralPrefix}Shared`],
      });
      sharedResult.error.issues.forEach((issue) => ctx.addIssue(issue));
    }
  })
  .transform((data) => data as GcrRegLine);
// satisfies z.ZodType<ScrLineFormInput>;
// }) satisfies z.ZodType<ScrLine>;

// Message schema:
export const gcrRegMessageSchema = z.object({
  airport: schema4charsOrBlank,
  si: z.string().optional(),
  gi: z.string().optional(),
}) satisfies z.ZodType<GcrRegMessage>;
