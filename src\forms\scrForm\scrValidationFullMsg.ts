import { validateActionSequence } from '@src/validation/actionSequenceValidation';
import { z } from 'zod';
import { scrLineSchema, scrMessageSchema } from './scrValidation';

export const fullScrMessageSchema = z
  .object({
    message: scrMessageSchema, // header/footer
    lines: z.array(scrLineSchema), // validated lines
  })
  .superRefine((data, ctx) => {
    const { lines } = data;

    // Validate C/R action sequence rules
    validateActionSequence(lines, ctx);
  });
