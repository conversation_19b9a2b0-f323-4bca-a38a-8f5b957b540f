import { createEmptyMessageWithDate, createFormStore, type FormStore } from '@src/formStore/createFormStore';
import type { ScrLine, ScrLineFormInput, ScrMessage } from './scrTypes';

const emptyLine: ScrLineFormInput = { action: 'N' };
const emptyMessage: Partial<ScrMessage> = createEmptyMessageWithDate();

export const useScrStore: FormStore<ScrLine, ScrMessage> = createFormStore<ScrLine, ScrMessage>({
  emptyLine,
  emptyMessage,
});
