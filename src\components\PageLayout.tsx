// import { useTheme } from '@mui/material/styles';
import styled from '@emotion/styled';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { appPages, PAGE_QUERY_PARAM } from '@src/constants';
import { useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export const PageSectionWrap = styled(Box)`
  overflow-x: auto;
  padding: 0.2rem;
  margin: 0 0 1.5rem 0;
`;

/**
 * Page layout component.
 * Defines the overall page structure and navigation.
 * It uses the router to determine the current page and render the appropriate content.
 */
export const PageLayout: React.FC = () => {
  // const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const activeColor = 'rgba(255, 255, 255, 0.1)';

  // Get current page from query parameters (case insensitive)
  const searchParams = new URLSearchParams(location.search);
  const currentPageParam = searchParams.get(PAGE_QUERY_PARAM)?.toLowerCase() || 'home';

  // Find the current page component
  const currentPage = useMemo(() => {
    return appPages.find((page) => page.pageParam.toLowerCase() === currentPageParam) || appPages[0];
  }, [currentPageParam]);

  /** Replace current page in the query parameters and navigate to the new page. */
  const handleNavigation = useCallback(
    (pageParam: string) => {
      const newSearchParams = new URLSearchParams(location.search);
      if (pageParam === 'home') {
        newSearchParams.delete(PAGE_QUERY_PARAM);
      } else {
        newSearchParams.set(PAGE_QUERY_PARAM, pageParam);
      }
      const newSearch = newSearchParams.toString();
      // Build the navigation path:
      const path = newSearch ? `/?${newSearch}` : '/';
      navigate(path);
    },
    [location.search, navigate],
  );

  return (
    <Box sx={{ minWidth: 'fit-content', width: '100%' }}>
      <AppBar
        position="static"
        sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', padding: '0.5rem 1rem' }}
      >
        <Box sx={{ mr: '5rem' }}>
          {appPages.map((page) => (
            <Button
              key={page.name}
              // color="inherit"
              // color="warning"
              onClick={() => handleNavigation(page.pageParam)}
              sx={{
                bgcolor: currentPage.pageParam === page.pageParam ? activeColor : 'transparent',
                fontSize: '0.9rem',
              }}
            >
              {page.name}
            </Button>
          ))}
        </Box>
      </AppBar>
      <Box
        style={{
          // border: '1px solid lime', // for debugging
          margin: '1.5rem auto',
          maxWidth: '1410px',
          minWidth: '1200px',
          width: '80%',
          // bgcolor: 'lime',
          // [theme.breakpoints.up('xs')]: {
          //   maxWidth: '100%',
          // },
          // [theme.breakpoints.up('sm')]: {
          //   maxWidth: '100%',
          // },
          // [theme.breakpoints.up('md')]: {
          //   maxWidth: '100%',
          // },
          // [theme.breakpoints.up('lg')]: {
          //   maxWidth: '100%',
          // },
        }}
      >
        <currentPage.component />
      </Box>
    </Box>
  );
};
