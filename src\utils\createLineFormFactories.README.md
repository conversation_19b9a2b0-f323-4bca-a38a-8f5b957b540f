# Line Form Factories

This module provides generic factories to eliminate code duplication across the three line form types (SCR, GCR FLT, GCR REG). The factories create consistent TextField components and utility functions with minimal configuration.

## Overview

Before the refactor, each form had ~50+ lines of nearly identical code for:

- Custom TextField components
- onChange handlers
- onKeyDown handlers for tab navigation and character selection
- Focus management
- Error handling

After the refactor, each form requires only ~9 lines of code using the generic factories.

## Factories

### `createLineTextField<TLine, TMessage>(config)`

Creates a custom TextField component for line forms.

**Configuration:**

```typescript
interface LineTextFieldConfig<TLine, TMessage> {
  formConfig: FormConfig<TLine>;
  useStore: () => BaseFormStore<TLine, TMessage>;
}
```

**Usage:**

```typescript
import { createLineTextField } from '@src/utils/createLineTextField';
import { scrLineFormConfig } from './scrLineFormConfig';
import type { ScrLine, ScrMessage } from './scrTypes';
import { useScrStore } from './useScrStore';

export const ScrLineTextField = createLineTextField<ScrLine, ScrMessage>({
  formConfig: scrLineFormConfig,
  useStore: useScrStore,
});
```

### `createLineFormUtils<TLine, TMessage>(config)`

Creates utility functions for form handling.

**Configuration:**

```typescript
interface LineFormUtilsConfig<TLine> {
  formConfig: FormConfig<TLine>;
}
```

**Usage:**

```typescript
import { createLineFormUtils } from '@src/utils/createLineFormUtils';
import { scrLineFormConfig } from './scrLineFormConfig';

const utils = createLineFormUtils({ formConfig: scrLineFormConfig });

// Use in components:
const onChange = utils.createOnChange('fieldName', setFormLinePartial);
const onKeyDown = utils.createOnKeyHandler(utils.inputs, 'fieldName', updateLine, setFocusKey);
```

## Features

### Automatic Field Type Detection

- **Select fields**: Automatically detected when `thisInput.type === 'select'`
- **Regular fields**: Text, number, etc.
- **Proper slotProps**: Different patterns for select vs regular fields for tabIndex

### Tab Navigation

- **Tab**: Move to next field in `tabOrder`
- **Shift+Tab**: Move to previous field in `tabOrder`
- **Focus management**: Automatic focus and visual indicators

### Character Selection (Select Fields)

- **Single key press**: Automatically selects matching option in select fields
- **Case insensitive**: Works with both uppercase and lowercase keys

### Value Transformation

- **Number fields**: Automatic string-to-number conversion
- **Uppercase fields**: Automatic uppercase transformation when `toUpperCase: true`

### Error Handling

- **Field-level errors**: Displays validation errors per field
- **Visual indicators**: Error styling and helper text

## Benefits

1. **Massive Code Reduction**: ~50+ lines → ~9 lines per form
2. **Consistency**: All forms behave identically
3. **Maintainability**: Changes only need to be made in one place
4. **Type Safety**: Full TypeScript support with proper generics
5. **Feature Parity**: All existing functionality preserved
6. **Easy Testing**: Generic factories can be unit tested once

## Current Implementation

All three forms now use the generic factories:

- **SCR Form**: `src/scrForm/SrcLineTextField.tsx`
- **GCR FLT Form**: `src/gcrFltForm/GcrFltTextField.tsx`
- **GCR REG Form**: `src/gcrRegForm/GcrRegLineTextField.tsx`

Each form's TextField component is now just a single factory call with the appropriate type parameters and configuration.

## Legacy Utils Files

The individual utils files (`scrLineFormTableUtils.ts`, `gcrFltLineFormUtils.ts`, `gcrRegLineFormUtils.ts`) can now be removed as their functionality is provided by the generic factories. However, they may still be referenced by existing table renderer components that haven't been fully migrated yet.
