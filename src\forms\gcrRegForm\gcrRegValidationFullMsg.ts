import { validateActionSequence } from '@src/validation/actionSequenceValidation';
import { z } from 'zod';
import { gcrRegLineSchema, gcrRegMessageSchema } from './gcrRegValidation';

export const fullGcrFltMessageSchema = z
  .object({
    message: gcrRegMessageSchema, // header/footer
    lines: z.array(gcrRegLineSchema), // validated lines
  })
  .superRefine((data, ctx) => {
    const { lines } = data;

    // Validate C/R action sequence rules
    validateActionSequence(lines, ctx);
  });
