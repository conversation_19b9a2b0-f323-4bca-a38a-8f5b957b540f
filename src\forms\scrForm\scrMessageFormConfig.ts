import type { FormConfig } from '@src/typesGlobal';
import type { ScrMessage } from './scrTypes';

export const scrMessageFormConfig: FormConfig<ScrMessage> = {
  inputs: {
    season: {
      storeKey: 'season',
      label: 'Season',
      type: 'text',
      toUpperCase: true,
    },
    date: {
      storeKey: 'date',
      label: 'Date',
      type: 'text',
      toUpperCase: true,
    },
    airport: {
      storeKey: 'airport',
      label: 'Airport',
      type: 'text',
      toUpperCase: true,
    },
    creator: {
      storeKey: 'creator',
      label: 'Creator',
      type: 'text',
    },
    si: {
      storeKey: 'si',
      label: 'SI',
      type: 'text',
    },
    gi: {
      storeKey: 'gi',
      label: 'GI',
      type: 'text',
    },
  },
};
