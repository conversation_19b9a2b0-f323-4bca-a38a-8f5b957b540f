import { isNil } from 'lodash';
import type { ScrLine, ScrMessage } from './scrTypes';

const val2str = <T = unknown>(val: T): string => (isNil(val) ? '' : String(val));

export const formatScrLine = (line: ScrLine): string => {
  const action = val2str(line.action);
  const operatorArr = val2str(line.operatorArr);
  const flightNumberArr = val2str(line.flightNumberArr?.padStart(4, '0'));
  const operatorDep = val2str(line.operatorDep);
  const flightNumberDep = val2str(line.flightNumberDep?.padStart(4, '0'));
  const dateFrom = val2str(line.dateFrom);
  const dateTo = val2str(line.dateTo);
  const doop = val2str(line.doop);
  const seats = val2str(line.seats?.toString().padStart(3, '0'));
  const aircraftType = val2str(line.aircraftType);
  const origin = val2str(line.origin);
  const previous = val2str(line.previous);
  const timeArr = val2str(line.timeArr);
  const timeDep = val2str(line.timeDep);
  const on = line?.on === 0 ? '' : val2str(line?.on);
  const next = val2str(line.next);
  const destination = val2str(line.destination);
  const stArr = val2str(line.stArr);
  const stDep = val2str(line.stDep);
  const frequency = val2str(line.frequency);
  const aircraftRegistration = val2str(line.aircraftRegistration);
  const acReg = aircraftRegistration ? `/ ${aircraftRegistration}/` : '';

  // Whether there is an arrival and departure:
  const isArr = origin || previous || timeArr || stArr;
  const isDep = timeDep || on || next || destination || stDep;
  // Create the arrival and departure strings:
  const arr1 = isArr ? ` ${origin}${previous}${timeArr} ` : '';
  const dep1 = isDep ? ` ${timeDep}${on}${next}${destination} ` : '';

  let result =
    `${action}${operatorArr}${flightNumberArr}` +
    ` ${operatorDep}${flightNumberDep}` +
    ` ${dateFrom}${dateTo} ${doop} ${seats}${aircraftType}` +
    `${arr1}${dep1}` +
    `${stArr}${stDep}${frequency} ${acReg}`;

  // Remove double spaces
  result = result.replace(/ {2,}/g, ' ').trim();

  // console.log({ result });
  return result;
};

export const formatScrMsgHeader = (msg: ScrMessage): string => {
  const creator = msg.creator ? `\n/${msg.creator}` : '';
  return `SCR${creator}\n${msg.season}\n${msg.date}\n${msg.airport}`;
};

export const formatScrMsgFooter = (msg: ScrMessage): string => {
  return `SI ${msg.si}\nGI ${msg.gi}`;
};

export const formatScrMessage = (msg: ScrMessage, lines: ScrLine[]): string => {
  const header = formatScrMsgHeader(msg);
  const linesAsStrings = lines.map(formatScrLine);
  const footer = formatScrMsgFooter(msg);
  return [header, ...linesAsStrings, footer].join('\n');
};
