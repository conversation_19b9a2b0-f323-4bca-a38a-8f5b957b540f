import { Typography } from '@mui/material';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import Paper from '@mui/material/Paper';
import { StructuralPrefix } from '@src/forms/gcrFltForm/gcrFltValidation';
import type { FormStore } from '@src/formStore/createFormStore';
import { validateActionSequence, type LineWithAction } from '@src/validation/actionSequenceValidation';
import { useEffect, useState } from 'react';

type LineErrorsProps<TLine extends LineWithAction, TMessage> = {
  useStore: FormStore<TLine, TMessage>;
};

/**
 * Displays errors for the line form.
 * Agnostic to the line type and store etc, ie can be used for all three line types.
 */
export const LineErrors = <TLine extends LineWithAction, TMessage>(props: LineErrorsProps<TLine, TMessage>) => {
  const { useStore } = props;

  const { lines, formLineErrors } = useStore();
  const [lineSeqErrors, setLineSeqErrors] = useState<Record<string, string>>({});

  // Check for C/R action sequence errors
  useEffect(() => {
    setLineSeqErrors(validateActionSequence(lines) || {});
  }, [lines]);

  const lineErrors = formLineErrors || {};
  // Sort out the errors that are structural (ie not for specific input fields):
  const errors = Object.keys(lineErrors).reduce<Record<string, string>>(
    (acc, curr) => {
      if (curr.startsWith(StructuralPrefix)) {
        acc[curr.replace(StructuralPrefix, '')] = lineErrors[curr];
      }
      return acc;
    },
    { ...lineSeqErrors },
  );

  const hasErrors = Object.keys(errors).length > 0;

  if (!hasErrors) return null;

  return (
    <Paper>
      <Box sx={{ mb: 2, p: '0.5rem' }}>
        <Typography variant="body1" sx={{ fontSize: '1rem', mb: 1 }}>
          Validation Errors
        </Typography>
        <List>
          {Object.keys(errors).map((key) => (
            <ListItem key={key} sx={{ py: 0.25 }}>
              <Typography component="span" color="error" sx={{ mr: 1.5 }}>
                {key}:{' '}
              </Typography>
              <Typography component="span"> {errors[key]}</Typography>
            </ListItem>
          ))}
        </List>
      </Box>
    </Paper>
  );
};
