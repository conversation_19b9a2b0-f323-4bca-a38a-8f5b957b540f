import { type BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig, FormConfigInput } from '@src/typesGlobal';

/**
 * Generic types for line form utilities
 */
type LineFormInputs<TLine> = FormConfig<TLine>['inputs'];
type LineFormUpdateLine<TLine, TMessage> = BaseFormStore<TLine, TMessage>['setFormLinePartial'];
type LineFormSetFocusKey<TLine> = (key: keyof TLine | null) => void;

/**
 * Configuration for creating line form utilities
 */
type LineFormUtilsConfig<TLine> = {
  formConfig: FormConfig<TLine>;
};

/**
 * Factory function to create line form utilities
 */
export const createLineFormUtils = <TLine, TMessage>(config: LineFormUtilsConfig<TLine>) => {
  const { formConfig } = config;

  /**
   * Creates an onChange handler for a specific field key.
   * Returns a function that takes an event and updates the store for that field,
   * given a specific field key and a store method to update the line.
   */
  const createOnChange =
    (key: keyof TLine, setFormLinePartial: LineFormUpdateLine<TLine, TMessage>) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      let value: string | number = e.target.value;

      // Check if this field should be a number type
      if (formConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
        value = Number(e.target.value);
      }

      // Apply uppercase transformation if configured
      if (formConfig.inputs[key]?.toUpperCase) {
        value = e.target.value?.toUpperCase();
      }

      setFormLinePartial({ [key]: value } as Partial<TLine>);
    };

  /**
   * Helper function to get the next input in tab order - or the previous if reverse is true.
   */
  const getNextTabInput = (inputs: LineFormInputs<TLine>, currentKey: keyof TLine, reverse: boolean = false) => {
    const currentIndex = inputs[currentKey].tabOrder || 0;
    const nextIndex = reverse ? currentIndex - 1 : currentIndex + 1;
    const nextInput = Object.values<FormConfigInput<TLine>>(inputs).find((input) => input.tabOrder === nextIndex);
    return nextInput;
  };

  /**
   * Creates a keyboard handler for tab navigation and character selection.
   * Returns a function that takes a keyboard event and handles tabbing and character entry,
   * given a specific input key, a store method to update the line, and a store method
   * to set focus key (ie which input field is currently focused).
   */
  const createOnKeyHandler =
    (
      inputs: LineFormInputs<TLine>,
      currentInputKey: keyof TLine,
      updateLine: LineFormUpdateLine<TLine, TMessage>,
      setFocusKey: LineFormSetFocusKey<TLine>,
    ) =>
    (e: React.KeyboardEvent<HTMLElement>) => {
      const thisInput = inputs[currentInputKey];

      // Handle character selection for select fields
      if (thisInput.type === 'select' && e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey) {
        const match = thisInput.options?.find((option) => option.toLowerCase() === e.key.toLowerCase());
        if (match) updateLine?.({ [currentInputKey]: match } as Partial<TLine>);
      }

      // Handle tab navigation
      if (e.key === 'Tab') {
        e.preventDefault();
        const nextInput = getNextTabInput(inputs, currentInputKey, e.shiftKey);
        setFocusKey(nextInput?.storeKey || null);

        if (nextInput) {
          const nextElement = document.querySelector(`[name="${String(nextInput.storeKey)}"]`);
          if (nextElement) (nextElement as HTMLInputElement)?.focus();
          nextElement?.setAttribute('aria-hidden', 'false');
        }
      }
    };

  return {
    createOnChange,
    createOnKeyHandler,
    inputs: formConfig.inputs,
  };
};
