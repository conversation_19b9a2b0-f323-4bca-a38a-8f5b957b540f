import { Home } from './components/HomePage';
import { GcrFltPage } from './forms/gcrFltForm/GcrFltPage';
import { GcrRegPage } from './forms/gcrRegForm/GcrRegPage';
import { ScrPage } from './forms/scrForm/ScrPage';

export const DEBUG = !!localStorage.getItem('DEBUG');

export const appPages = [
  { name: 'Aviation Messages', pageParam: 'home', component: Home },
  { name: 'SCR', pageParam: 'scr', component: ScrPage },
  { name: 'GCR FLT', pageParam: 'gcrFlt', component: GcrFltPage },
  { name: 'GCR Reg', pageParam: 'gcrReg', component: GcrRegPage },
];

// Query parameter name for page routing
export const PAGE_QUERY_PARAM = 'diyssimPage';
