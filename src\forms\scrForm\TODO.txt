(Done) Make overall line form validation:
- If 'Arrival' has value, then all upper fields are required
- If 'Departure' has value, then all lower fields are required, except O/N
- In both cases, Frq and Acreg are optional

(Done) Improve DOOP validation and formatting:
- Allow 0 padding between numbers, if so 7 characters exactly
- zeros only allowed with full format 7 characters
- alternative is shorthand without zeros
- Make a method that converts shorthand to full format and use live in the form (onBlur) as well output

(Done) Expand on C and R lines validation:
- C (one or more) must always be followed by at least one R (this is implemented)
- R must always be preceded by at least one C (not implemented yet)
- C and R must be in the same block (may not be necessary, as the rules above cover it)

(Done) Adapt aircraft reg schema
- 3-7 characters (google aircraft registration format requirements)

(Done) Adapt aircraft type schema
- Must be 3 chars exactly, required

(Done) Time format validation HHMM:
- check values HH 00-23, MM 00-59

(Done) Adapt flight number schema
- Allow suffix character A-Z (auto uppercase)
- Make form convert lower to upper case (as implemented in several other fields)
