import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { PageSectionWrap } from '@src/components/PageLayout';
import { GcrRegLineFormContainer } from './GcrRegLineFormContainer';
import { GcrRegLineListContainer } from './GcrRegLineListContainer';
import { GcrRegMessageFormContainer } from './GcrRegMessageFormContainer';
import { GcrRegMessageOutputContainer } from './GcrRegMessageOutputContainer';
import { GcrRegMessagePreviewContainer } from './GcrRegMessagePreviewContainer';

export const GcrRegPage: React.FC = () => {
  return (
    <>
      <Box sx={{ height: '1.5rem' }} />
      <Grid container spacing={1} sx={{ alignItems: 'flex-start' }}>
        <Grid sx={{ mr: '1rem', width: '40%', minWidth: '90rem', maxWidth: '100rem' }}>
          <PageSectionWrap /* sx={{ maxWidth: '99%' }} */>
            <GcrRegLineFormContainer />
          </PageSectionWrap>
        </Grid>
        <Grid sx={{ minWidth: '30rem', maxWidth: '40rem', width: '100%' /* , mt: '-0.75rem' */ }}>
          <PageSectionWrap>
            <GcrRegLineListContainer />
          </PageSectionWrap>
        </Grid>
      </Grid>
      <Box sx={{ display: 'flex', justifyContent: 'center', m: 0 }}>
        <Box sx={{ width: '100%', maxWidth: '50rem', m: 0 }}>
          <PageSectionWrap /* sx={{ maxWidth: '50rem' }} */>
            <GcrRegMessageFormContainer />
          </PageSectionWrap>
          <PageSectionWrap>
            <GcrRegMessagePreviewContainer />
          </PageSectionWrap>
          <PageSectionWrap>
            <GcrRegMessageOutputContainer />
          </PageSectionWrap>
        </Box>
      </Box>
    </>
  );
};
