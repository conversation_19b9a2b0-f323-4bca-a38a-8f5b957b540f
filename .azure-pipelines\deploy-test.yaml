trigger:
  branches:
    include:
      - develop

pool:
  name: <PERSON><PERSON><PERSON>

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '20.x'
    displayName: 'Set up Node.js'

  - script: |
      corepack enable
      corepack prepare pnpm@latest --activate
    displayName: 'Set up pnpm'

  - checkout: self
    path: s/repo
    clean: false
    displayName: 'Git Checkout'

  - script: |
      pnpm install --store-dir="../pnpm-cache" --prefer-offline --frozen-lockfile
    displayName: 'Install dependencies (with local cache folder)'

  - script: |
      pnpm build
    displayName: 'Build app'

  - script: |
      powershell -Command "if (Test-Path dist.zip) { Remove-Item dist.zip }; Compress-Archive -Path dist\* -DestinationPath dist.zip"
    displayName: 'Zip the build files'

  - task: PublishBuildArtifacts@1
    inputs:
      PathtoPublish: 'dist.zip'
      ArtifactName: 'diyssim-build'
      publishLocation: 'Container'
    displayName: 'Publish build artifact'

  - task: AzureWebApp@1
    displayName: 'Deploy to Azure Web App'
    inputs:
      azureSubscription: 'Azure PDC webhosting'
      appType: 'webApp'
      appName: 'diyssim-test'
      resourceGroupName: 'rgPdcWebHosting'
      package: '$(System.DefaultWorkingDirectory)/dist.zip' # string. Required. Package or folder. Default: $(System.DefaultWorkingDirectory)/**/*.zip.
      # Required when appType != webAppLinux && appType != "" && package NotEndsWith .war && package NotEndsWith .jar. Deployment method. Default: auto.
      deploymentMethod: 'zipDeploy' # 'auto' | 'zipDeploy' | 'runFromPackage'.
